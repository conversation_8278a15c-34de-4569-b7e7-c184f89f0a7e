import request from '@/utils/request'

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/device/list',
    method: 'get',
    params: query
  })
}

// 查询设备详细
export function getDevice(id) {
  return request({
    url: '/device/' + id,
    method: 'get'
  })
}

// 新增设备配置
export function addDevice(data) {
  return request({
    url: '/device',
    method: 'post',
    data: data
  })
}

// 修改设备配置
export function updateDevice(data) {
  return request({
    url: '/device',
    method: 'put',
    data: data
  })
}

// 删除设备配置
export function delDevice(ids) {
  return request({
    url: '/device/' + ids,
    method: 'delete'
  })
}

// 导出设备
export function exportDevice(query) {
  return request({
    url: '/device/export',
    method: 'get',
    params: query
  })
}

// 启用
export function enable(id) {
  return request({
    url: '/device/enable/' + id,
    method: 'post'
  })
}

// 停用
export function disable(id) {
  return request({
    url: '/device/disable/' + id,
    method: 'post'
  })
}

// 查询采集终端
export function unboundList() {
  return request({
    url: '/am/unboundList',
    method: 'get'
  })
}

// 查询采集终端
export function allAmList() {
  return request({
    url: '/am/allList',
    method: 'get'
  })
}

// 传输状态修改
export function changeTrStatus(id, status) {
  return request({
    url: '/device/changeTrStatus/' + id + '/' + status,
    method: 'post'
  })
}

// 查询参数列表
export function getFreeSerialPort() {
  return request({
    url: '/device/getFreeSerialPort',
    method: 'get'
  })
}

// 按设备类型查询属性列表
export function attributeList(type) {
  return request({
    url: '/deviceAttribute/listByType',
    method: 'get',
    params: {"type": type}
  })
}

export function transferAttributeList(code) {
  return request({
    url: '/transferAttribute/listByDeviceCode',
    method: 'get',
    params: {"deviceCode": code}
  })
}

// 开启串口监听
export function serialPortView(data) {
  return request({
    url: '/device/serialPortView',
    method: 'post',
    data: data
  })
}

// 关闭串口监听
export function delSerialPortView(data) {
  return request({
    url: '/device/delSerialPortView',
    method: 'post',
    data: data
  })
}

// 查询设备列表
export function getTableHead(deviceId) {
  return request({
    url: '/device/getTableHead/' + deviceId,
    method: 'get',
  })
}

// 查询设备列表
export function getTypeTableHead(type) {
  return request({
    url: '/device/getTypeTableHead/' + type,
    method: 'get',
  })
}

// 按设备类型模板查询默认属性列表
export function listDefaultsByType(type) {
  return request({
    url: '/transferAttribute/listDefaultsByType',
    method: 'get',
    params: {"type": type}
  })
}
