<template>
  <div class="app-container" id="deviceDiv">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入设备名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="deviceList">
      <el-table-column label="设备名称" align="center" prop="name" :show-overflow-tooltip="true"/>
      <el-table-column label="参数名称" align="center" prop="attributes" :formatter="attributesFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="传输间隔" align="center" prop="compartmentStr" :show-overflow-tooltip="true"/>
      <el-table-column label="优先级" align="center" prop="cost" :show-overflow-tooltip="true"/>
      <el-table-column label="连接状态" align="center" prop="connectStatus" :formatter="connectStatusFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="最新数据时间" align="center" prop="lastTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预览" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="openRealTimeData(scope.row)"
            v-hasPermi="['snapshot:transfer:edit']"
          >预览
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="存储状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enable"
            :active-value="1"
            :inactive-value="0"
            @change="changeStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="传输状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.transferStatus"
            :active-value="1"
            :inactive-value="0"
            @change="changeTrStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <!--<el-table-column label="已选(个数)" align="center" prop="attributes" :formatter="attributesNumFormat"-->
                       <!--:show-overflow-tooltip="true"/>-->

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['transfer:attribute:edit']"
          >修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getDeviceList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px">
      <el-form ref="form" :model="form" label-width="80px">
        <!--<el-row>-->
          <!--<el-col :span="15" >-->
        <el-form-item label="设备名称" label-width="80px" prop="deviceName" >
          <el-input v-model="form.deviceName" :disabled="true"/>
        </el-form-item>
          <!--</el-col>-->
        <!--</el-row>-->
        <div class="edit_dev">
          <el-transfer
            :titles="['未选属性', '已选属性']"
            v-model="form.transferAttributes"
            :props="{
              key: 'name'
            }"
            :data="allAttributes" >
          </el-transfer>
        </div>
        <el-form-item label="传输间隔" prop="compartment">
          <el-input v-model="form.compartment" placeholder="请输入传输间隔" v-show="false"/>
          <el-select v-model="compartmentNum">
            <el-option
              v-for="num in numOptions"
              :key="num"
              :label="num"
              :value="num"
            ></el-option>
          </el-select>
          <el-select v-model="compartmentUnit">
            <el-option
              v-for="unit in unitOptions"
              :key="unit.val"
              :label="unit.label"
              :value="unit.val"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="cost">
          <el-select v-model="form.cost" placeholder="请选择优先级">
            <el-option
              v-for="co in costOptions"
              :key="co"
              :label="co"
              :value="co"
            ></el-option>
          </el-select>
        </el-form-item>
       </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!--实时数据弹出框-->
    <el-dialog :title="rtTitle" :visible.sync="rtOpen" width="800px" @close="rtCancel">
      <div>
        <div>
          <el-button type="primary" @click="initWebSocket" v-show="!wsConnect">连接</el-button>
          <el-button type="danger" @click="closeWebsocket" v-show="wsConnect">暂停</el-button>
          <el-button type="primary" @click="cleanShowDiv">清空</el-button>
        </div>

        <div class="show" id="showDiv"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    attributeList,
    listDevice,
    transferAttributeList,
    updateTransferAttribute,
    changeStatus,
    changeTrStatus
  } from '@/api/device/transferAttribute'
  import $ from 'jquery'
  export default {
    name: 'TransferAttribute',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 参数表格数据
        deviceList: [],
        allAttributes: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: undefined
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          compartment: [
            {required: true, message: '传输间隔不能为空', trigger: 'blur'}
          ]
        },
        rtTitle: '',
        rtOpen: false,
        rtMac: '',
        socket: null,
        sid: null,
        wsConnect: false,
        wsMessageLength: 0,
        // 优先级
        costOptions: [10, 20, 30, 40, 50],
        // 传输间隔
        numOptions: [1, 2, 3, 4, 5, 6, 10, 12, 15, 20, 30],
        // 传输间隔
        unitOptions: [{val: 1, label: '秒'}, {val: 2, label: '分'}],
        compartmentNum: 1,
        compartmentUnit: 1,
        cost:10,
        type:null
      }
    },
    created() {
      this.getDeviceList();
    },
    methods: {
      /** 查询设备列表 */
      getDeviceList() {
        this.loading = true;
        listDevice(this.addDateRange(this.queryParams, [])).then(response => {
            this.deviceList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        )
      },
      getAttributeList(type) {
        attributeList(type).then(response => {
            this.allAttributes = response.data;
          }
        )
      },
      getTransferAttributeList(deviceCode) {
        let _this = this;
        _this.form.deviceCode = deviceCode;
        transferAttributeList(deviceCode).then(response => {
          // _this.form.deviceName=response.data[0].deviceName;
          // _this.form.compartment=response.data[0].compartment;
          // _this.form.cost=response.data[0].cost;
          response.data.forEach(item => {
            _this.form.transferAttributes.push(item.name);
            });
          if (_this.form.compartment >= 60) {
            _this.compartmentNum = _this.form.compartment / 60;
            _this.compartmentUnit = 2;
          } else {
            _this.compartmentNum = _this.form.compartment;
            _this.compartmentUnit = 1;
          }
          }
        );

      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          deviceCode: undefined,
          transferAttributes: []
        };
        this.resetForm('form');
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getDeviceList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.form.compartment = row.compartment;
        this.form.cost = row.cost;
        this.form.deviceName = row.name;
        this.getTransferAttributeList(row.code);
        this.getAttributeList(row.type);
        this.title = '修改属性';
        this.open = true;
      },
      /** 提交按钮 */
      submitForm: function () {
        this.compartmentChange();
        updateTransferAttribute(this.form).then(response => {
          if (response.code === 200) {
            this.msgSuccess('修改成功');
            this.open = false;
            this.getDeviceList();
          } else {
            this.msgError(response.msg);
          }
        })
      },
      attributesFormat(row, column) {
        if (row.attributes === undefined || row.attributes === '' || row.attributes === null) {
          return '';
        }
        let arr = row.attributes.split(',');
        let newStr='';
        for (let i = 0; i < arr.length; i++) {
          newStr += '【' + arr[i] + '】';
        }
        return newStr;
      },
      attributesNumFormat(row, column) {
        if (row.attributes === undefined || row.attributes === '' || row.attributes === null) {
          return '';
        }
        let arr = row.attributes.split(',');
        return arr.length;
      },
      connectStatusFormat(row, column) {
        if (row.connectStatus === 1) {
          return "连接";
        }
        return "中断";
      },
      /** 存储状态修改 */
      changeStatus(row) {
        let that = this;
        let text = row.enable === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.name + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return changeStatus(row.code, row.enable);
        }).then(() => {
          this.msgSuccess(text + '成功');
        }).catch(function () {
          row.enable = row.enable === 0 ? 1 : 0;
        });
      },
      /** 传输状态修改*/
      changeTrStatus(row) {
        let that = this;
        let text = row.transferStatus === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.name + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return changeTrStatus(row.code, row.transferStatus);
        }).then(() => {
          this.msgSuccess(text + '成功');
        }).catch(function () {
          row.transferStatus = row.transferStatus === 0 ? 1 : 0;
        });
      },
      /** 数据窗口弹出 */
      openRealTimeData(row) {
        this.rtOpen = true;
        this.rtTitle = '实时数据';
        // this.rtMac = row.mac;
        this.type = row.type;
        if (row.connectType === 0) {
          this.rtMac = row.mac;//采集终端预览
          this.initWebSocket();
        } else {
          this.rtMac = row.serialPort;//串口预览
          this.initWebSocket();
        }
        // this.initWebSocket();
      },
      /** 数据窗口关闭 */
      rtCancel() {
        this.closeWebsocket();
        this.cleanShowDiv();
        this.rtOpen = false;
      },
      //初始化websocket
      initWebSocket() {
        this.sid = 'sid-' + (new Date()).getTime();
        const wsUri = 'ws://' + window.location.host + ':9991/wsServer/' + this.sid + '/' + this.rtMac+ '/' + 0+ '/' + this.type;
        this.socket = new WebSocket(wsUri);
        this.socket.onmessage = this.websocketOnmessage;
        this.socket.onopen = this.websocketOnopen;
        this.socket.onerror = this.websocketOnerror;
        this.socket.onclose = this.websocketClose;

        this.monitorFun(1);
      },
      websocketOnopen() { //连接建立之后执行send方法发送数据
        console.log('websocket连接成功');
        this.wsConnect = true;
      },
      websocketOnerror() {//连接建立失败重连
        console.log('websocket连接失败');
        this.wsConnect = false;
      },
      websocketOnmessage(e) { //数据接收
        this.wsMessageLength++;

        if (this.wsMessageLength === 1) {
          $('.show').append('<p id="sm_' + this.wsMessageLength + '">' + e.data + '</p>');
        } else {
          $('#sm_' + (this.wsMessageLength - 1)).after('<p id="sm_' + this.wsMessageLength + '">' + e.data + '</p>');
        }

        if (this.wsMessageLength > 1000) {
          $('#sm_' + (this.wsMessageLength - 1000)).remove();
        }

        let showDiv = document.getElementById('showDiv');
        showDiv.scrollTop = showDiv.scrollHeight;

      },
      websocketClose() {
        console.log('websocket断开连接');
        this.wsConnect = false;
      },
      closeWebsocket() {
        this.socket.close();
      },
      cleanShowDiv() {
        $('.show').empty();
        this.wsMessageLength = 0;
      },
      monitorFun(t) {
        let time = t || 2;
        let userTime = time * 60;
        let that = this;
        let objTime = {
          init: 0,
          time: function () {
            objTime.init += 1;
            if (!that.wsConnect) {
              objTime.destroyFun();
            }

            if (objTime.init === userTime) {
              that.closeWebsocket();
              objTime.destroyFun();
            }
          },
          eventFun: function () {
            objTime.clearFun();
            monitorTest = setInterval(objTime.time, 1000);
          },
          clearFun: function () {
            clearInterval(monitorTest);
            objTime.init = 0;
          },
          destroyFun: function () {
            objTime.clearFun();
            $('#deviceDiv').unbind('click').unbind('keydown').unbind('mousemove').unbind('mousewheel');
          }
        };
        let monitorTest = setInterval(objTime.time, 1000);

        $('#deviceDiv').bind('click', objTime.eventFun).bind('keydown', objTime.eventFun)
          .bind('mousemove', objTime.eventFun).bind('mousewheel', objTime.eventFun);
      },
      compartmentChange() {
        // this.form.cost = this.cost;
        this.form.compartment = this.compartmentNum;
        if (this.compartmentUnit === 2) {
          this.form.compartment = this.compartmentNum * 60;
        }
      }
    }
  }
</script>

<style scoped>
  .edit_dev{
    padding-bottom: 20px;
    padding-top:20px;
  }
  .edit_dev >>> .el-transfer-panel {
    width: 320px;
  }
  .show {
    width: 100%;
    height: 370px;
    overflow-y: scroll;
    padding: 10px;
    background-color: black;
    color: white;
    margin: 3px 0;
  }
</style>
