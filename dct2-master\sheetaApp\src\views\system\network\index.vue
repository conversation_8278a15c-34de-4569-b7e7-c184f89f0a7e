<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

    </el-row>

    <el-table v-loading="loading" :data="networkList">
      <el-table-column label="网口名称" align="center" prop="type"/>
      <el-table-column label="配置模式" align="center" prop="mode"/>
      <el-table-column label="ip地址" align="center" prop="ip"/>
      <el-table-column label="子网掩码" align="center" prop="netmask"/>
      <el-table-column label="网关" align="center" prop="gateway"/>
      <el-table-column label="DNS1" align="center" prop="dns1"/>
      <el-table-column label="DNS2" align="center" prop="dns2"/>
      <el-table-column label="访问网段1" align="center" prop="visit1"/>
      <el-table-column label="访问网段2" align="center" prop="visit2"/>
      <el-table-column label="访问网段3" align="center" prop="visit3"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:network:edit']"
            v-if="scope.row.ip !== currentIp"
          >修改
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="网口名称" prop="type">
          <el-input v-model="form.type" readonly/>
        </el-form-item>
        <el-form-item label="配置模式" prop="mode">
          <el-select v-model="form.mode" placeholder="请选择">
            <el-option
              v-for="m in modeOptions"
              :key="m"
              :label="m"
              :value="m"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="ip地址" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入ip地址" :disabled="form.mode === 'DHCP'"/>
        </el-form-item>
        <el-form-item label="子网掩码" prop="netmask">
          <el-input v-model="form.netmask" placeholder="请输入子网掩码" :disabled="form.mode === 'DHCP'"/>
        </el-form-item>
        <el-form-item label="网关" prop="gateway">
          <el-input v-model="form.gateway" placeholder="请输入网关" :disabled="form.mode === 'DHCP'"/>
        </el-form-item>
        <el-form-item label="DNS1" prop="dns1" v-if="form.type === 'WAN'">
          <el-input v-model="form.dns1" placeholder="请输入DNS1"/>
        </el-form-item>
        <el-form-item label="DNS2" prop="dns2" v-if="form.type === 'WAN'">
          <el-input v-model="form.dns2" placeholder="请输入DNS2"/>
        </el-form-item>
        <el-form-item label="访问网段1" prop="visit1" v-if="form.type === 'LAN'">
          <el-input v-model="form.visit1" placeholder="访问网段1"/>
        </el-form-item>
        <el-form-item label="访问网段2" prop="visit2" v-if="form.type === 'LAN'">
          <el-input v-model="form.visit2" placeholder="访问网段2"/>
        </el-form-item>
        <el-form-item label="访问网段3" prop="visit3" v-if="form.type === 'LAN'">
          <el-input v-model="form.visit3" placeholder="访问网段3"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="recov">重 置</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="rtsBeforeTitle" :visible.sync="rtsBeforeOpen" width="160px" height="150px" >
      <div class="demo-progress" id="progress">
        <el-progress type="dashboard" :percentage="percentage2" :color="colors"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {getNetworkList, updateNetwork} from "@/api/system/network";

  export default {
    name: "Network",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 总条数
        total: 0,
        // 表格数据
        networkList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 类型数据字典
        modeOptions: ['DHCP', 'STATIC'],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {},
        currentIp: '',
        rtsBeforeTitle: '',
        rtsBeforeOpen: false,
        percentage2: 0,
        interval:null,
        colors: [
          {color: '#f56c6c', percentage: 20},
          {color: '#e6a23c', percentage: 40},
          {color: '#5cb87a', percentage: 60},
          {color: '#1989fa', percentage: 80},
          {color: '#6f7ad3', percentage: 100},
        ],
        originalValue: undefined
      };
    },
    created() {
      this.currentIp = window.location.hostname;
      console.log(this.currentIp);
      this.getList();
    },
    methods: {
      /** 查询参数列表 */
      getList() {
        this.loading = true;
        getNetworkList().then(response => {
            this.networkList = response.data;
            this.total = this.networkList.length;
            for (let i = 0; i < this.networkList.length; i++) {
              if (this.networkList[i].name === 'enp1s0' || this.networkList[i].name === 'wan') {
                this.networkList[i].type = 'WAN';
              }
              if (this.networkList[i].name === 'enp2s0' || this.networkList[i].name === 'lan') {
                this.networkList[i].type = 'LAN';
              }
            }
            this.loading = false;
          }
        );
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
        this.getList();
      },
      // 表单重置
      reset() {
        this.form = {
          name: undefined,
          type: undefined,
          mode: undefined,
          ip: undefined,
          netmask: undefined,
          gateway: undefined,
          dns1: undefined,
          dns2: undefined,
          visit1: undefined,
          visit2: undefined,
          visit3: undefined
        };
        this.resetForm("form");
        this.originalValue = undefined;
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.originalValue = JSON.parse(JSON.stringify(row));
        this.form = JSON.parse(JSON.stringify(row));
        this.open = true;
        this.title = "修改参数";
        if (this.form.type === 'WAN') {
          this.modeOptions = ['DHCP', 'STATIC'];
        } else if (this.form.type === 'LAN') {
          this.modeOptions = ['STATIC'];
        }
      },
      recov() {
        this.form = JSON.parse(JSON.stringify(this.originalValue));
      },
      /** 提交按钮 */
      submitForm: function () {
        let _this = this;
        _this.percentage2 = 0;
        _this.rtsBeforeOpen = true;
        _this.rtsBeforeTitle = '';
        clearInterval(_this.interval);
        _this.interval = null;
        _this.interval = setInterval(() => {
          _this.percentage2 = (_this.percentage2 % 100) + 5;
          if (_this.percentage2 === 100) {
            clearInterval(_this.interval);
            _this.rtsBeforeOpen = false;

          }
        }, 1000);
        updateNetwork(_this.form).then(response => {
          if (response.code === 200) {
            _this.rtsBeforeOpen = false;
            _this.percentage2 = 100;
            _this.msgSuccess("修改成功");
            _this.open = false;
            _this.getList();
          } else {
            _this.rtsBeforeOpen = false;
            _this.percentage2 = 100;
            this.msgError(response.msg);
          }
        }).catch(function () {
          _this.rtsBeforeOpen = false;
          _this.percentage2 = 100;
        });
      }
    }
  };
</script>
