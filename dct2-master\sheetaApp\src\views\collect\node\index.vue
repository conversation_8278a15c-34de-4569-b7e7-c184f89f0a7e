<template>
  <div class="app-container" id="deviceDiv">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="节点名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入节点名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['collect:node:add']"
        >新增
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="nodeList">
      <el-table-column label="节点名称" align="center" prop="name" :show-overflow-tooltip="true"/>
      <el-table-column label="模块型号" align="center" prop="model" :show-overflow-tooltip="true"/>
      <el-table-column label="有线网口-DHCP" align="center" prop="netDhcp" :show-overflow-tooltip="true" :formatter="netDhcpStatusFormat"/>
      <el-table-column label="有线网口-IP" align="center" prop="netIp" :show-overflow-tooltip="true"/>
      <el-table-column label="有线网口-子网掩码" align="center" prop="netSubnetMask" :show-overflow-tooltip="true"/>
      <el-table-column label="有线网口-网关" align="center" prop="netGateway" :show-overflow-tooltip="true"/>
      <el-table-column label="是否采集传输" align="center" prop="isCollect" :show-overflow-tooltip="true" :formatter="isCollectStatusFormat"/>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['collect:node:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['collect:node:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px">
      <el-form ref="form" :model="form" :rules="rules" label-width="180px" v-loading="submitLoading">
        <el-form-item label="连接串口" prop="serialPort">
          <el-select v-model="form.serialPort" placeholder="请选择串口">
            <el-option
              v-for="sp in serialPortOptions"
              :key="sp"
              :label="sp"
              :value="sp"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="模块型号" prop="model">
          <el-select v-model="form.model" placeholder="请选择模块型号">
            <el-option
              v-for="mo in modelOptions"
              :key="mo"
              :label="mo"
              :value="mo"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="节点名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入节点名称"/>
        </el-form-item>

        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>有线网口设置</span>
          </div>
          <div>
            <el-form-item label="DHCP" prop="netDhcp">
              <el-switch
                v-model="form.netDhcp"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </el-form-item>
            <el-form-item label="IP地址" prop="netIp" v-if="form.netDhcp === 0">
              <el-input v-model="form.netIp" placeholder="请输入IP地址"/>
            </el-form-item>
            <el-form-item label="子网掩码" prop="netSubnetMask" v-if="form.netDhcp === 0">
              <el-input v-model="form.netSubnetMask" placeholder="请输入子网掩码"/>
            </el-form-item>
            <el-form-item label="网关" prop="netGateway" v-if="form.netDhcp === 0">
              <el-input v-model="form.netGateway" placeholder="请输入网关"/>
            </el-form-item>
          </div>
        </el-card>

        <el-form-item label="数据采集传输" prop="isCollect">
          <el-switch
            v-model="form.isCollect"
            :active-value="1"
            :inactive-value="0"
          ></el-switch>
        </el-form-item>
        <el-form-item label="采集模块名称" prop="moduleName" v-if="form.isCollect === 1">
          <el-input v-model="form.moduleName" placeholder="请输入采集模块名称"/>
        </el-form-item>
        <el-card class="box-card" v-if="form.isCollect === 1">
          <div slot="header" class="clearfix">
            <span>串口参数设置</span>
          </div>
          <div>
            <el-form-item label="波特率" prop="baudRate">
              <el-select v-model="form.baudRate" placeholder="请选择波特率" >
                <el-option
                  v-for="dt in baudRateOptions"
                  :key="dt.dictValue"
                  :label="dt.dictLabel"
                  :value="dt.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="数据位" prop="dataBits">
              <el-select v-model="form.dataBits" placeholder="请输入数据位" >
                <el-option
                  v-for="dt in dataBitsOptions"
                  :key="dt.dictValue"
                  :label="dt.dictLabel"
                  :value="dt.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="停止位" prop="stopBits">
              <el-select v-model="form.stopBits" placeholder="请输入停止位" >
                <el-option
                  v-for="dt in stopBitsOptions"
                  :key="dt.dictValue"
                  :label="dt.dictLabel"
                  :value="dt.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="校验位" prop="parity">
              <el-select v-model="form.parity" placeholder="请输入校验位" >
                <el-option
                  v-for="dt in parityOptions"
                  :key="dt.dictValue"
                  :label="dt.dictLabel"
                  :value="dt.dictValue"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="船舶终端服务IP" prop="shipIp">
              <el-input v-model="form.shipIp" placeholder="请输入船舶终端服务IP"/>
            </el-form-item>
          </div>
        </el-card>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="rtsBeforeTitle" :visible.sync="rtsBeforeOpen" width="300px" height="100px">
      <div class="demo-progress" id="progress">
        <el-progress  :indeterminate="true" :percentage="percentage2" :color="colors" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    addCollectNode,
    collectNodeList,
    delCollectNode,
    getAllSerialPort,
    getCollectNode,
    updateCollectNode,
    getWanIp
  } from '@/api/collect/node'

  export default {
    name: 'CollectNode',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 总条数
        total: 0,
        // 参数表格数据
        nodeList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: undefined
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          name: [
            {required: true, message: '节点名称不能为空', trigger: 'blur'}
          ],
          serialPort: [
            {required: true, message: '串口号不能为空', trigger: 'blur'}
          ],
          model: [
            {required: true, message: '模块型号不能为空', trigger: 'blur'}
          ],
          baudRate: [
            {required: true, message: '波特率不能为空', trigger: 'blur'}
          ],
          dataBits: [
            {required: true, message: '数据位不能为空', trigger: 'blur'}
          ],
          stopBits: [
            {required: true, message: '停止位不能为空', trigger: 'blur'}
          ],
          parity: [
            {required: true, message: '校验位不能为空', trigger: 'blur'}
          ],
          shipIp: [
            {required: true, message: '船舶终端服务IP不能为空', trigger: 'blur'}
          ],
          moduleName: [
            {required: true, message: '采集模块名称不能为空', trigger: 'blur'}
          ]
        },
        // 串口列表
        serialPortOptions: [],
        modelOptions: ['XH-RS2020WS-2','XH-NEWWS-2'],
        //波特率
        baudRateOptions:[],
        //数据位
        dataBitsOptions:[],
        //停止位
        stopBitsOptions:[],
        //校验位
        parityOptions:[],
        percentage2: 0,
        colors: [
          {color: '#f56c6c', percentage: 20},
          {color: '#e6a23c', percentage: 40},
          {color: '#5cb87a', percentage: 60},
          {color: '#1989fa', percentage: 80},
          {color: '#6f7ad3', percentage: 100},
        ],
        rtsBeforeTitle: '',
        rtsBeforeOpen: false,
        submitLoading: false
      }
    },
    created() {
      this.getList();
      this.getDicts("baud_rate").then(response => {
        this.baudRateOptions = response.data;
      });
      this.getDicts("date_bits").then(response => {
        this.dataBitsOptions = response.data;
      });
      this.getDicts("stop_bits").then(response => {
        this.stopBitsOptions = response.data;
      });
      // this.getDicts("parity").then(response => {
      //   this.parityOptions = response.data;
      // });
      this.getDicts("parity").then(response => {
        for (let i = 0; i < response.data.length; i++) {
          let typeMap = {dictValue: 0, dictLabel: ''};
          typeMap.dictValue = parseInt(response.data[i].dictValue);
          typeMap.dictLabel = response.data[i].dictLabel;
          this.parityOptions[i] = typeMap;
        }
      })
    },
    methods: {
      /** 查询列表 */
      getList() {
        this.loading = true;
        collectNodeList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
            this.nodeList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        )
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: undefined,
          serialPort: undefined,
          model: undefined,
          name: undefined,
          netDhcp: 0,
          netIp: undefined,
          netSubnetMask: undefined,
          netGateway: undefined,
          isCollect: 1,
          baudRate: undefined,
          dataBits: undefined,
          stopBits: undefined,
          parity: undefined,
          shipIp: undefined
        };
        this.resetForm('form');
        this.submitLoading = false;
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = '添加采集节点';
        this.form.netSubnetMask="*************";
        this.getAllSerialPort();
        this.form.baudRate = 9600;
        this.form.dataBits = 8;
        this.form.stopBits = 1;
        this.form.parity = 0;
        getWanIp().then(response => {
          for (let i = 0; i < response.data.length; i++) {
            if (response.data.length >1){
              this.form.shipIp = response.data[1];
            } else{
              this.form.shipIp = response.data[0];
            }
          }

        })
      },
      getAllSerialPort() {
        this.loading = true;
        getAllSerialPort().then(response => {
            this.serialPortOptions = response.data;
            this.loading = false;
          }
        )
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.getAllSerialPort();
        getCollectNode(row.id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = '修改参数';
        });
      },
      /** 提交按钮 */
      submitForm: function () {
        this.$refs['form'].validate(valid => {
          if (valid) {
            this.percentage2 = 0;
            this.rtsBeforeOpen = true;
            this.rtsBeforeTitle = '采集节点努力配置中...';
            if (this.form.id !== undefined) {
              updateCollectNode(this.form).then(response => {
                this.rtsBeforeOpen = false;
                if (response.code === 200) {
                  this.msgSuccess('修改成功');
                  this.open = false;
                  this.getList();
                } else {
                  this.submitLoading = false;
                  this.msgError(response.msg);
                }
              })
            } else {
              addCollectNode(this.form).then(response => {
                this.rtsBeforeOpen = false;
                if (response.code === 200) {
                  this.msgSuccess('新增成功');
                  this.open = false;
                  this.getList();
                } else {
                  this.submitLoading = false;
                  this.msgError(response.msg);
                }
              })
            }
            let interval = setInterval(() => {
              this.percentage2 = (this.percentage2 % 100) + 1;
              if (this.percentage2 === 100) {
                clearInterval(interval);
                this.rtsBeforeOpen = false;
              }
            }, 700);
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        let _this = this;
        this.$confirm('是否确认删除选择的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          delCollectNode(row.id).then(res => {
            if (res.code === 200) {
              _this.getList();
              _this.msgSuccess('删除成功');
            }
          })
        }).catch(function () {
        })
      },
      netDhcpStatusFormat(row, column) {
        if (row.netDhcp === 1) {
          return "开启";
        }
        return "关闭";
      },
      isCollectStatusFormat(row, column) {
        if (row.isCollect === 1) {
          return "是";
        }
        return "否";
      }
    }
  }
</script>

