<template>
  <div class="app-container">
    <div>
      配置方式<el-select v-model="config" placeholder="请选择" style="margin:0px 60px" @change="configChange(config)">
      <el-option
        v-for="item in configs"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </el-option>
    </el-select>
    </div>
    <div style="margin:30px 0px">
      时区<el-select v-model="timeZone" placeholder="请选择" style="margin:0px 90px" @change="timeChange(timeZone)">
      <el-option
        v-for="item in timeZones"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </el-option>
    </el-select>
    </div>
    <div v-if="config==='config3'">
      <div style="margin:30px 0px">
        时间
        <el-date-picker
        v-model="selectTime"
        type="datetime"
        placeholder="选择日期时间"
        @change="changeSelectTime"
        style="margin:0px 85px">
      </el-date-picker>
      </div>
    </div>
    <div v-else>
      <div style="margin:30px 0px">
        日期 <span style="margin:0px 90px" v-if="showView">{{this.getData}}</span>
      </div>
      <div style="margin:30px 0px">
        系统时间 <span style="margin:0px 60px" v-if="showView">{{this.getTime}}</span>
      </div>
    </div>
    <div v-if="show" style="margin:30px 0px">
      NTP服务器IP <el-input v-model="IP" style="margin:0px 20px;width:200px" ></el-input>
      端口<el-input v-model="port" style="margin:0px 30px;width:200px"></el-input>
    </div>
    <div v-if="!show">
      <el-button type="primary" @click="editTime" :loading="loading">确定</el-button>
    </div>
  </div>
</template>

<script>
import { editTimeByManual,getSysTime,synchronizationTime } from "@/api/system/time";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Time",
  data() {
    return {
      configs:[{
        value: 'config1',
        label: '从NTP服务器同步时间'
      },{
        value: 'config2',
        label: '同步阿里云时间'
      },{
        value: 'config3',
        label: '手动修改时间'
      }],
      timeZones:[
        {
          value: 'timeZone8',
          label: 'UTC 8 北京'
        }
      ],
      config: 'config1',
      timeZone: 'timeZone8',
      show: true,
      IP: '',
      port: '',
      getData: '',
      getTime: '',
      df: [],
      showView: true,
      loading: false,
      selectTime: '',
      timer: '',
    };
  },
  created() {
    getSysTime().then(res=>{
      this.df = res.data.split(' ');
      this.getData = this.df[0];
      this.getTime = this.df[1];
    })
  },
  mounted() {
    this.timer = setInterval(this.refreshView,1000);
  },
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
  },
  methods: {
    changeSelectTime(){
      var _this = this;
      if(_this.selectTime !== null && _this.selectTime !== '' && _this.selectTime !== undefined ){
        _this.selectTime =_this.selectTime.getFullYear() + '-' +
          (_this.selectTime.getMonth() + 1) + '-' + _this.selectTime.getDate()
          + ' ' + _this.selectTime.getHours() + ':' + _this.selectTime.getMinutes()
          + ':' + _this.selectTime.getSeconds();
      }
      console.log(_this.selectTime)
    },
    configChange(data){
      var _this = this;
      if(data === 'config1'){
        _this.show = true;
      }else if(data === 'config2' || data === 'config3'){
        _this.show = false;
      }
      getSysTime().then(res=>{
        _this.df = res.data.split(' ');
        _this.getData = this.df[0];
        _this.getTime = this.df[1];
      })
    },
    editTime(){
      var _this = this;
      _this.loading = true;
      if(_this.config === 'config2'){
        synchronizationTime().then(res=>{
          if (res.code === 200) {
            getSysTime().then(res=>{
              _this.df = res.data.split(' ');
              _this.getData = this.df[0];
              _this.getTime = this.df[1];
            })
            _this.msgSuccess("同步时间成功");
            _this.loading = false;
          } else {
            _this.msgError(res.msg);m
          }
        })
      }else if(_this.config === 'config3'){
        editTimeByManual(_this.selectTime).then(res=>{
          if (res.code === 200) {
            _this.msgSuccess("修改时间成功");
          } else {
            _this.msgError(res.msg);
          }
        })
        _this.loading = false;
      }

    },
    refreshView () {
      var _this = this;
      getSysTime().then(res=>{
        _this.df = res.data.split(' ');
        _this.getData = this.df[0];
        _this.getTime = this.df[1];
      })
      this.showView = false
      this.$nextTick(() => {
        this.showView = true
      })
    }
  }
};
</script>
