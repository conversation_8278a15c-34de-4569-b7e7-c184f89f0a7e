import request from '@/utils/request'

// 查询采集节点配置列表
export function collectNodeList(query) {
  return request({
    url: '/collect/node/list',
    method: 'get',
    params: query
  })
}

//查询采集节点配置详细
export function getCollectNode(id) {
  return request({
    url: '/collect/node/'+ id,
    method: 'get'
  })
}

//修改采集节点配置
export function updateCollectNode(data) {
  return request({
    url:'/collect/node',
    method:'put',
    data:data
  })
}

//新增采集节点配置
export function addCollectNode(data) {
  return request({
    url:'/collect/node',
    method:'post',
    data:data
  })
}
//删除采集节点配置
export function delCollectNode(id) {
  return request({
    url:'/collect/node/'+ id,
    method: 'delete'
  })
}

// 查询参数列表
export function getAllSerialPort() {
  return request({
    url: '/device/getAllSerialPort',
    method: 'get'
  })
}

// 查询wanip
export function getWanIp() {
  return request({
    url: '/collect/module/wanIp/getIp',
    method: 'get'
  })
}
