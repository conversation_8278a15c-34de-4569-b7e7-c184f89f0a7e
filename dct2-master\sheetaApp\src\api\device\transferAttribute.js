import request from '@/utils/request'

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/transferAttribute/list',
    method: 'get',
    params: query
  })
}

// 按设备类型查询属性列表
export function attributeList(type) {
  return request({
    url: '/deviceAttribute/listByType',
    method: 'get',
    params: {"type": type}
  })
}

export function transferAttributeList(code) {
  return request({
    url: '/transferAttribute/listByDeviceCode',
    method: 'get',
    params: {"deviceCode": code}
  })
}

export function updateTransferAttribute(data) {
  return request({
    url: '/transferAttribute',
    method: 'put',
    data: data
  })
}

// 存储状态修改
export function changeStatus(code, status) {
  return request({
    url: '/transferAttribute/changeStatus/' + code + '/' + status,
    method: 'post'
  })
}
// 传输状态修改
export function changeTrStatus(code, status) {
  return request({
    url: '/transferAttribute/changeTrStatus/' + code + '/' + status,
    method: 'post'
  })
}
