<template>
  <div class="app-container">
    <!--        设备接入情况-->
    <div class="situation">
      <div class="situation_title">
        <img src="../assets/icons/img/title-line.png" class="title_line">
        <span class="title-text" style="font-family: 'PingFang SC'">当前设备接入情况</span>
      </div>
      <div class="situation_num">
        <div class="situation_num_son" id="son_1">
          <img src="../assets/icons/img/shebei.png" width="38px" height="28px">
          设备接入数
          <span style="float: right;margin-right: 24px;font-size: 16px;">{{this.jieruNum}}/{{this.jieruTotal}}</span>
        </div>

        <div class="situation_num_son" id="son_2" style="font-family: 'PingFang SC'">
          <img src="../assets/icons/img/canshu.png"  width="34px" height="32px">
          设备参数量
          <span style="float: right;margin-right: 50px;font-size: 16px;">{{ this.canshuNum }}/{{this.canshuTotal}}</span>
        </div>

        <div class="situation_num_son" id="son_3">
          <img src="../assets/icons/img/cemera.png"  width="34px" height="28px">
          快照通道数
          <span style="float: right;margin-right: 50px;font-size: 16px;">{{this.kuaizhaoNum}}/{{this.kuaizhaoTotal}}</span>
        </div>
      </div>
    </div>

    <div class="main">
      <div class="main-left">

        <div class="situation_title" style="padding-left: 0">
          <img src="../assets/icons/img/title-line.png" class="title_line" >
          <span class="title-text">设备状态</span>
        </div>

        <div class="info-left">
          <div  class="info-bottom">

            <div style="width: 33%;text-align: center;white-space: pre">
              <el-tooltip  placement="right"  effect="light" popper-class="system-tooltip">
                <div slot="content">{{ this.cpuInfo1 }}<br>{{this.cpuInfo2}}<br>{{this.cpuInfo3}}</div>
                <el-progress   :color="cpuColor"  type="circle" :percentage="cpuValue " :width="145" :height="145" ></el-progress>
              </el-tooltip>
              <span style="font-size: 16px;color: #444444;font-family: 'PingFang SC'">CPU</span>
            </div>


            <div style="width: 33%;text-align: center">
              <el-tooltip  placement="right"  effect="light" popper-class="system-tooltip">
                <div slot="content">{{ this.memoryInfo1 }}<br>{{this.memoryInfo2}}<br>{{this.memoryInfo3}}</div>
                <el-progress  :color="memColor"    type="circle" :percentage="memory " :width="145" :height="145" style="font-size: 12px" ></el-progress>
              </el-tooltip>
              <span style="font-size: 16px;color: #444444;font-family: 'PingFang SC'" >内存</span>
            </div>


            <div style="width: 33%;text-align: center" >
              <el-tooltip  placement="right" effect="light" popper-class="system-tooltipLast">
                <div slot="content">{{ this.hardSoftInfo }}</div>
                <el-progress :color="hardSoftColor"  type="circle" :percentage="hardSoft" :width="145" :height="145"></el-progress>
              </el-tooltip>
              <span style="font-size: 16px;color: #444444;font-family: 'PingFang SC'">硬盘</span>
            </div>


          </div>
        </div>

        <!--数据采集传输情况-->
        <div class="transition">

          <div style="display: flex;justify-content: space-between;height: 40px;line-height: 40px">
            <div class="situation_title" style="padding-left: 0">
              <img src="../assets/icons/img/title-line.png" class="title_line" >
              <span class="title-text">当前数据传输量</span>
            </div>

            <div class="selectDiv">
              <el-select v-model="selected" placeholder="请选择" @change="optionChange">
                <el-option
                  v-for="item in options"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"/>
              </el-select>
            </div>
          </div>
          <div class="e_box" style="display: flex">
            <div id="data_num" style="width: 300px;height: 240px"></div>
            <div id="data_transition"  style="width: 300px;height: 240px"></div>
            <div id="average_speed"  style="width: 450px;height: 250px"></div>
          </div>

        </div>
      </div>

      <div class="main-right">
        <!--        <span class="title_line"></span>-->
        <!--          <span >设备信息</span>-->
        <div class="situation_title" style="padding-left: 0">
          <img src="../assets/icons/img/title-line.png" class="title_line" >
          <span class="title-text" style="font-family: 'PingFang SC'">设备消息</span>
        </div>
        <div  class="info" style="margin-bottom: 13px;margin-top: 15px">
          <div style="margin-top: 24px;margin-bottom: 20px;font-family: 'PingFang SC'" >设备序列号:123456</div>
          <div style="margin-bottom: 20px;font-family: 'PingFang SC'">版本信息:xh-tm001 v0001r0003</div>
          <div style="margin-bottom: 20px;font-family: 'PingFang SC'" >时钟信息: {{ this.serverTime }}</div>
          <div style="margin-bottom: 24px;font-family: 'PingFang SC'">运行时长: {{ this.timeDay }}天{{this.timeHour}}小时{{this.timeMin}}分</div>
        </div>
        <!--      系统运行状态-->
        <div class="run_state">
          <div class="situation_title" style="padding-left: 0">
            <img src="../assets/icons/img/title-line.png" class="title_line" >
            <span class="title-text">系统运行状态</span>
          </div>
          <div style="width:100%;
        height: 290px;
         display: flex;
         overflow-y: auto;
         overflow-x: hidden;
         box-shadow: 0 2px 12px 0 rgba(0,0,0,0.5);
         margin-top: 30px;
        overflow-x: hidden;
        padding-left: 24px;
        padding-right: 24px">
            <div  style="margin-right: 20px;font-size: 14px;color: #666666">
              <div v-for="item in operTime"  style="margin-bottom: 10px;padding-top: 10px;padding-left: 10px">
                {{item}}
              </div>
            </div>
            <div style="font-size: 16px;padding-top: 10px;font-size: 14px;color: #444444">
              <div v-for="item in title"  style="margin-bottom: 20px;">
                {{item}}
              </div>
            </div>
          </div>

        </div>

      </div>
    </div>
  </div>
</template>

<script>
  // 获取设备信息
  import { getServer  } from "@/api/monitor/server";

  // 获取系统运行状态,时钟信息，运行时长
  import  { getSysRunStatus,getDateTime,getSecondsTime  } from "@/api/index/index";

  // 获取设备接入数,设备参数量，快照通道数据
  import  { getAccessCount , getDeviceParamsCount,getSnapShotChannelCount  } from "@/api/index/index";

  // 获取采集的数据
  import  {getAcquisitionTransmission,getdataDeviceList,getdataSnapshotList } from "@/api/index/index";

  // 获取硬件使用率
  import {getDeskUsage} from "@/api/index/index";

  // 导入momont.js插件
  import moment from "moment";



  export default {
    name: "Server",
    data() {
      return {
        title_collect:'',
        title_transform:'',
        cpuColor:'#5894ff',
        memColor:'#5894ff',
        hardSoftColor:'#5894ff',
        cpuInfo1:'',
        cpuInfo2:'',
        cpuInfo3:'',
        memoryInfo1:'',
        memoryInfo2:'',
        memoryInfo3:'',
        hardSoftInfo:'暂无数据',
        // 加载层信息
        loading: [],
        // 服务器信息
        server: [],
        // 设备信息参数
        cpuValue:0,
        memory:0,
        hardSoft:0,
        serverTime:'',
        totalTime:'',
        timeDay:'',
        timeHour:'',
        timeMin:'',
        // 设备接入情况饼图参数
        jieruNum:0,
        jieruTotal:0,
        canshuNum:0,
        canshuTotal:0,
        kuaizhaoNum:0,
        kuaizhaoTotal:0,
        selected: '1',
        options: [
          { key:'3',label:'最近一个月' },
          { key:'2',label:'最近一天'},
          { key:'1',label:'最近一小时'}
        ],
        // 折线图设备和快照的数据
        deviceList:[],
        snapShotList:[],
        deviceList1:[],
        snapShotList1:[],
        xValue:[],
        //系统运行状态参数
        title:[],
        operTime:[],
      };
    },
    created() {
      this.cpuInfo1=''
      this.cpuInfo2=''
      this.cpuInfo3=''
      this.memoryInfo1='',
        this.memoryInfo2='',
        this.memoryInfo3='',
        this.getList();
      this.openLoading();
      // 默认情况下最近一小时的设备数据
      getdataDeviceList(this.selected).then(response=>{
        if(response.data.length == 0){
          this.deviceList=[0,0,0,0,0,0]
        }
        else{
          let tempList=[]
          let sum=0
          // 将字符串转化为数值类型
          for(let i in response.data){
            tempList.push(response.data[i])
          }

          let now = new Date()
          // 当前时间到间隔10分钟的一小时之前
          let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
          let  six=[]
          let tenMins=moment(now).subtract(10,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let five=[]
          let twentyMins=moment(now).subtract(20,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let four=[]
          let thirtyMins=moment(now).subtract(30,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let three=[]
          let fourtyMins=moment(now).subtract(40,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let two=[]
          let fiftyMins=moment(now).subtract(50,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let one=[]
          let sixtyMins=moment(now).subtract(60,'minutes').format('YYYY-MM-DD HH:mm:ss')


          // 对时间进行判断
          for(let i in tempList){

            if(tempList[i].createTime>=sixtyMins&&tempList[i].createTime<fiftyMins){
              one.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=fiftyMins&&tempList[i].createTime<fourtyMins){
              two.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=fourtyMins&&tempList[i].createTime<thirtyMins){
              three.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=thirtyMins&&tempList[i].createTime<twentyMins){
              four.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=twentyMins&&tempList[i].createTime<tenMins){
              five.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=tenMins&&tempList[i].createTime<nowTime){
              six.push(parseFloat(tempList[i].characterLength))
            }
          }

          if(one.length==0){
            this.deviceList.push(0)
          }
          else{
            for(let i in one){
              sum+=parseFloat(one[i])
            }
            this.deviceList.push((sum/10).toFixed(5))
            sum=0
          }

          if(two.length==0){
            this.deviceList.push(0)
          }
          else{
            for(let i in two){
              sum+=parseFloat(two[i])
            }
            this.deviceList.push((sum/10).toFixed(5))
            sum=0
          }

          if(three.length ==0){
            this.deviceList.push(0)
          }
          else{
            for(let i in three){
              sum+=parseFloat(three[i])
            }
            this.deviceList.push((sum/10).toFixed(5))
            sum=0
          }

          if(four.length == 0){
            this.deviceList.push(0)
          }
          else{
            for(let i in four){
              sum+=parseFloat(four[i])
            }
            this.deviceList.push((sum/10).toFixed(5))
            sum=0
          }

          if(five.length == 0){
            this.deviceList.push(0)
          }
          else{
            for(let i in five){
              sum+=parseFloat(five[i])
            }
            this.deviceList.push((sum/10).toFixed(5))
            sum=0
          }

          if(six.length == 0){
            this.deviceList.push(0)
          }
          else{
            for(let i in six){
              sum+=parseFloat(six[i])
            }
            this.deviceList.push((sum/10).toFixed(5))
            sum=0
          }

        }
      })
      // 默认情况下最近一小时的快照数据
      getdataSnapshotList(this.selected).then(response=>{
        if(response.data.length == 0){
          this.snapShotList=[0,0,0,0,0,0]
        }
        else{
          let tempList=[]
          let sum=0
          // 将字符串转化为数值类型
          for(let i in response.data){
            tempList.push(response.data[i])
          }

          let now = new Date()
          // 当前时间到间隔10分钟的一小时之前
          let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
          let  six=[]
          let tenMins=moment(now).subtract(10,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let five=[]
          let twentyMins=moment(now).subtract(20,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let four=[]
          let thirtyMins=moment(now).subtract(30,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let three=[]
          let fourtyMins=moment(now).subtract(40,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let two=[]
          let fiftyMins=moment(now).subtract(50,'minutes').format('YYYY-MM-DD HH:mm:ss')
          let one=[]
          let sixtyMins=moment(now).subtract(60,'minutes').format('YYYY-MM-DD HH:mm:ss')
          // 对时间进行判断
          for(let i in tempList){
            if(tempList[i].createTime>=sixtyMins&&tempList[i].createTime<fiftyMins){
              one.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=fiftyMins&&tempList[i].createTime<fourtyMins){
              two.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=fourtyMins&&tempList[i].createTime<thirtyMins){
              three.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=thirtyMins&&tempList[i].createTime<twentyMins){
              four.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=twentyMins&&tempList[i].createTime<tenMins){
              five.push(parseFloat(tempList[i].characterLength))
            }
            if(tempList[i].createTime>=tenMins&&tempList[i].createTime<nowTime){
              six.push(parseFloat(tempList[i].characterLength))
            }
          }


          if(one.length ==0){
            this.snapShotList.push(0)
          }
          else{
            for(let i in one){
              sum+=parseFloat(one[i])
            }
            this.snapShotList.push((sum/10).toFixed(5))
            sum=0
          }

          if(two.length ==0){
            this.snapShotList.push(0)
          }
          else{
            for(let i in two){
              sum+=parseFloat(two[i])
            }
            this.snapShotList.push((sum/10).toFixed(5))
            sum=0
          }

          if(three.length==0){
            this.snapShotList.push(0)
          }
          else{
            for(let i in three){
              sum+=parseFloat(three[i])
            }
            this.snapShotList.push((sum/10).toFixed(5))
            sum=0
          }

          if(four.length==0){
            this.snapShotList.push(0)
          }
          else{
            for(let i in four){
              sum+=parseFloat(four[i])
            }
            this.snapShotList.push((sum/10).toFixed(5))
            sum=0
          }

          if(five.length==0){
            this.snapShotList.push(0)
          }
          else{
            for(let i in five){
              sum+=parseFloat(five[i])
            }
            this.snapShotList.push((sum/10).toFixed(5))
            sum=0
          }

          if(six.length==0){
            this.snapShotList.push(0)
          }
          else{
            for(let i in six){
              sum+=parseFloat(six[i])
            }
            this.snapShotList.push((sum/10).toFixed(5))
          }

        }
      })
      // 获取图的配置项数据
      getAcquisitionTransmission(this.selected).then(response=>{

        // 通过返回的数据判断采集数据量和传输数据量的大小设置单位
        if(response.data[0]>1024){
          this.title_collect='GB'
          response.data[0]=(parseFloat(response.data[0])/1024).toFixed(2)
          response.data[1]=(parseFloat(response.data[1])/1024).toFixed(2)
          response.data[2]=(parseFloat(response.data[2])/1024).toFixed(2)
        }
        else{
          this.title_collect='MB'
          response.data[0]=parseFloat(response.data[0]).toFixed(2)
          response.data[1]=parseFloat(response.data[1]).toFixed(2)
          response.data[2]=parseFloat(response.data[2]).toFixed(2)
        }
        if(response.data[3]>1024){
          this.title_transform='GB'
          response.data[3]=(parseFloat(response.data[3])/1024).toFixed(2)
          response.data[4]=(parseFloat(response.data[4])/1024).toFixed(2)
          response.data[5]=(parseFloat(response.data[5])/1024).toFixed(2)
        }
        else{
          this.title_transform='MB'
          response.data[3]=parseFloat(response.data[3]).toFixed(2)
          response.data[4]=parseFloat(response.data[4]).toFixed(2)
          response.data[5]=parseFloat(response.data[5]).toFixed(2)
        }
        let now = new Date()
        let nowTime=moment(now).format('HH:mm')
        let tenMins=moment(now).subtract(10,'minutes').format('HH:mm')
        let twentyMins=moment(now).subtract(20,'minutes').format('HH:mm')
        let thirtyMins=moment(now).subtract(30,'minutes').format('HH:mm')
        let fourtyMins=moment(now).subtract(40,'minutes').format('HH:mm')
        let fiftyMins=moment(now).subtract(50,'minutes').format('HH:mm')
        let sixtyMins=moment(now).subtract(60,'minutes').format('HH:mm')
        this.xValue=[sixtyMins,fiftyMins,fourtyMins,thirtyMins,twentyMins,tenMins,nowTime]
        // 第一个图表配置
        let first = this.$echarts.init(document.getElementById('data_num'))
        first.setOption({
          title: {
            text: '采集数据量 '+response.data[0]+this.title_collect,
            left: 'center',
            top:'10px',
            textStyle: {
              fontSize:'14',
              color: "#444444"
            },
          },
          tooltip: {
            trigger: 'item',
            transitionDuration:0
          },
          legend:{
            bottom:0,
            left:'25%',
            itemHeight: 12,
            itemWidth: 12,
            data: [
              {
                name: '快照数据',
                icon: 'circle',
              },
              {
                name: '设备数据',
                icon: 'circle',
              }
            ]
          },

          series: [
            {
              type: 'pie',
              radius: '50%',
              data: [
                { value: response.data[1], name: '设备数据',itemStyle: {
                    normal:{
                      color: "#5894ff"
                    }
                  } },
                { value: response.data[2], name: '快照数据',itemStyle: {
                    normal:{
                      color: "#ffbe46"
                    }
                  } }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        })
        // 第二个图表配置
        let second = this.$echarts.init(document.getElementById('data_transition'))
        second.setOption({
          title: {
            text: '传输数据量 '+response.data[3]+this.title_transform,
            left: 'center',
            top:'10px',
            textStyle: {
              fontSize:'14',
              color: "#444444"
            },
          },
          tooltip: {
            trigger: 'item',
            transitionDuration:0
          },
          legend:{
            bottom:0,
            left:'25%',
            itemHeight: 12,
            itemWidth: 12,
            data: [
              {
                name: '快照数据',
                icon: 'circle',
              },
              {
                name: '设备数据',
                icon: 'circle',
              }
            ]
          },
          series: [
            {
              type: 'pie',
              radius: '50%',
              data: [
                { value: response.data[4], name: '设备数据',itemStyle: {
                    normal:{
                      color: "#5894ff"
                    }
                  } },
                { value: response.data[5], name: '快照数据',itemStyle: {
                    normal:{
                      color: "#ffbe46"
                    }
                  } }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        })
        //第三个折线图配置
        let third=this.$echarts.init(document.getElementById('average_speed'))
        third.setOption({
          title: {
            text: '平均传输速率',
            textStyle: {
              fontSize:'14',
              color: "#444444"
            },
            top:'10'
          },
          tooltip: {
            trigger: 'axis',
            transitionDuration:0
          },
          grid: {
            left: '3%',
            right: '-3%',
            bottom: '2%',
            top:'27%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap:true ,
            data: this.xValue,
            axisLabel: {
              // rotate:40,
              // padding: [0, 0, 0, -40] ,   // 四个数字分别为上右下左与原位置距离
              inside: false,
              textStyle: {
                color: '#b2b2b2',
                fontSize: '10',
                itemSize: ''
              }
            },
            axisLine:{
              lineStyle:{
                color:'#b2b2b2',
              }
            }

          },
          yAxis: {
            type: 'value',
            name:this.title_transform+'/分',
            axisLine: {
              show: false,//不显示坐标轴轴线
              lineStyle:{
                color:'#b2b2b2',
                fontSize:'10'
              }
            },
            axisTick: {
              show: false  //不显示坐标轴刻度
            },

          },
          series: [
            {
              name: '设备数据',
              type: 'line',
              data: this.deviceList,
              itemStyle : {
                normal : {
                  color:'#5894ff',
                  lineStyle:{
                    color: "#5894ff"
                  }
                }
              },
            },
            {
              name: '快照数据',
              type: 'line',
              data: this.snapShotList,
              itemStyle: {
                color:'#ffbe46',
                normal:{
                  color: "#ffbe46"
                }
              }
            }
          ]
        })

      })
      // 时钟信息单独设置刷新时间
      // setInterval(this.clockInfo,1000)
      // 定时刷新页面其他数据
      // setInterval(this.getList,60000)
      // 图像定时刷新
      // setInterval(this.reload,60000)

    },
    methods: {
      // 打开加载层
      openLoading() {
        this.loading = this.$loading({
          lock: true,
          text: "拼命读取中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)"
        });
      },
      clockInfo(){
        // 获取时钟信息的数据
        getDateTime().then(response =>{
          this.serverTime=response.msg
          let now =new Date(this.serverTime)
        })
      },
      getList(){
        // 获取服务器运行时长的数据
        getSecondsTime().then(response =>{
          this.totalTime= parseInt(response.msg)

          this.timeDay=Math.floor(this.totalTime/(60*24*60));
          this.timeHour=Math.floor((this.totalTime-(this.timeDay*60*24*60))/3600)
          this.timeMin=Math.floor((this.totalTime-(this.timeDay*60*24*60)-(this.timeHour*60*60))/60)
          if(this.timeHour<=9){
            this.timeHour='0'+this.timeHour
          }
          if(this.timeMin<=9){
            this.timeMin='0'+this.timeMin
          }
        })
        // 查询服务器信息
        getServer().then(response => {
          this.server = response.data;
          this.loading.close();

          // 配置上方圆环的数据
          // cpu数据获取
          this.cpuValue=parseFloat((this.server.cpu.sys+this.server.cpu.used).toFixed(2));
          // if(this.cpuValue>0&&this.cpuValue<=50){
          //   this.cpuColor='green'
          // }
          // if(this.cpuValue>50&&this.cpuValue<=75){
          //
          //   this.cpuColor='yellow'
          // }
          // if(this.cpuValue>75){
          //
          // this.cpuColor='red'
          // }

          this.cpuInfo1="用户使用率"+this.server.cpu.sys+'%'
          this.cpuInfo2="系统使用率"+this.server.cpu.used+'%'
          this.cpuInfo3= '剩余空闲率'+(100-this.cpuValue).toFixed(2)+'%'

          // 内存数据获取
          this.memory=parseFloat((this.server.mem.usage).toFixed(2))
          // if(this.memory>0&&this.memory<=50){
          //
          //   this.memColor='green'
          // }
          // if(this.memory>50&&this.memory<=75){
          //
          //   this.memColor='yellow'
          // }
          // if(this.memory>75){
          //
          //     this.memColor='red'
          // }

          this.memoryInfo1="总内存"+this.server.mem.total+'GB'
          this.memoryInfo2="使用内存"+this.server.mem.used+'GB'
          this.memoryInfo3="剩余内存"+this.server.mem.free+'GB'
        });
        // 获取设备信息
        getDeskUsage().then(response=>{
          this.hardSoft=response.data
          // if(this.hardSoft>0&&this.hardSoft<=50){
          //   this.hardSoftColor='green'
          // }
          // if(this.hardSoft>50&&this.hardSoft<=75){
          //   this.hardSoftColor='yellow'
          // }
          // if(this.hardSoft>75){
          //   this.hardSoftColor='red'
          // }

          this.hardSoftInfo='系统使用率'+response.data+'%'
        })
        // 获取数据采集情况的接入量数据
        getAccessCount().then(response =>{

          this.jieruNum=response.data[0]
          this.jieruTotal=response.data[1]
        });
        // 获取数据采集情况的设备参数量数据
        getDeviceParamsCount().then(response=>{

          this.canshuNum=response.data[0]
          this.canshuTotal=response.data[1]
        })
        // 获取数据采集情况的快照通道数数据
        getSnapShotChannelCount().then(response=>{
          this.kuaizhaoNum=response.data[0]
          this.kuaizhaoTotal=response.data[1]
        })
        // 获取系统运行状态的数据
        getSysRunStatus().then(response=>{

          for (let i of response.data) {
            this.title.push(i.title)
            this.operTime.push(i.operTime)
          }
        })
      },
      // 根据选项的变更传递不同的参数，时间从小到大为1-3
      optionChange(selected)  {
        // 根据参数设置不同的X轴刻度值
        let now = new Date()
        let tenMins=moment(now).subtract(10,'minutes').format('HH:mm:')
        let twentyMins=moment(now).subtract(20,'minutes').format('HH:mm')
        let thirtyMins=moment(now).subtract(30,'minutes').format('HH:mm')
        let fourtyMins=moment(now).subtract(40,'minutes').format('HH:mm')
        let fiftyMins=moment(now).subtract(50,'minutes').format('HH:mm')
        let sixtyMins=moment(now).subtract(60,'minutes').format('HH:mm')
        let hour4=moment(now).subtract(4,'hours').format('DD日HH时')
        let hour8=moment(now).subtract(8,'hours').format('DD日HH时')
        let hour12=moment(now).subtract(12,'hours').format('DD日HH时')
        let hour16=moment(now).subtract(16,'hours').format('DD日HH时')
        let hour20=moment(now).subtract(20,'hours').format('DD日HH时')
        let hour24=moment(now).subtract(24,'hours').format('DD日HH时')
        let day5=moment(now).subtract(5,'days').format('MM月DD日')
        let day10=moment(now).subtract(10,'days').format('MM月DD日')
        let day15=moment(now).subtract(15,'days').format('MM月DD日')
        let day20=moment(now).subtract(20,'days').format('MM月DD日')
        let day25=moment(now).subtract(25,'days').format('MM月DD日')
        let day30=moment(now).subtract(30,'days').format('MM月DD日')
        if(this.selected == '1'){
          let nowTime=moment(now).format('HH:mm')
          this.xValue=[sixtyMins,fiftyMins,fourtyMins,thirtyMins,twentyMins,tenMins,nowTime]
          console.log(this.xValue)
        }
        if(this.selected=='2'){
          let nowTime=moment(now).format('DD日HH时')
          this.xValue=[hour24,hour20,hour16,hour12,hour8,hour4,nowTime]
          console.log(this.xValue)
        }
        if(this.selected =='3'){
          let nowTime=moment(now).format('MM月DD日')
          this.xValue=[day30,day25,day20,day15,day10,day5,nowTime]
          console.log(this.xValue)
        }

        // 折线图快照信息的获取
        getdataSnapshotList(this.selected).then(response=>{

          // 最近一小时的快照数据
          if(this.selected =='1'){
            this.snapShotList=[]
            let now = new Date()
            // 当前时间到间隔10分钟的一小时之前
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let tenMins=moment(now).subtract(10,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let twentyMins=moment(now).subtract(20,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let thirtyMins=moment(now).subtract(30,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let fourtyMins=moment(now).subtract(40,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let fiftyMins=moment(now).subtract(50,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let sixtyMins=moment(now).subtract(60,'minutes').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断

            for(let i in response.data){
              if(response.data[i].createTime>=sixtyMins&&response.data[i].createTime<fiftyMins){
                one.push(response.data[i].characterLength)
              }
              if(response.data[i].createTime>=fiftyMins&&response.data[i].createTime<fourtyMins){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=fourtyMins&&response.data[i].createTime<thirtyMins){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=thirtyMins&&response.data[i].createTime<twentyMins){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=twentyMins&&response.data[i].createTime<tenMins){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=tenMins&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0

            if(one.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            // console.log(this.snapShotList)
          }

          // 最近一天的快照数据
          if(this.selected =='2'){
            this.snapShotList=[]
            let now = new Date()
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let hour4=moment(now).subtract(4,'hours').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let hour8=moment(now).subtract(8,'hours').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let hour12=moment(now).subtract(12,'hours').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let hour16=moment(now).subtract(16,'hours').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let hour20=moment(now).subtract(20,'hours').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let hour24=moment(now).subtract(24,'hours').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断
            for(let i in response.data){
              if(response.data[i].createTime>=hour24&&response.data[i].createTime<hour20){
                one.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour20&&response.data[i].createTime<hour16){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour16&&response.data[i].createTime<hour12){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour12&&response.data[i].createTime<hour8){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour8&&response.data[i].createTime<hour4){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour4&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum =0
            if(one.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }

              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }


            if(two.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in two){

                sum+=parseFloat(two[i])
              }

              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }

              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }

              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }

              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.snapShotList.push(0)
            }
            else {
              for(let i in six){
                sum+=parseFloat(six[i])
              }

              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            console.log(this.snapShotList)


          }

          // // 最近一个月的快照数据
          if(this.selected=='3'){
            this.snapShotList=[]
            let now = new Date()
            // 一个月之间的间隔点
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let day5=moment(now).subtract(5,'days').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let day10=moment(now).subtract(10,'days').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let day15=moment(now).subtract(15,'days').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let day20=moment(now).subtract(20,'days').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let day25=moment(now).subtract(25,'days').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let day30=moment(now).subtract(30,'days').format('YYYY-MM-DD HH:mm:ss')



            // 对时间进行判断
            for(let i in response.data){
              if(response.data[i].createTime>=day30&&response.data[i].createTime<day25){
                one.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day25&&response.data[i].createTime<day20){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day20&&response.data[i].createTime<day15){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day15&&response.data[i].createTime<day10){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day10&&response.data[i].createTime<day5){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day5&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0
            if(one.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in two){

                sum+=parseFloat(two[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.snapShotList.push(0)
            }
            else {
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            // console.log(this.snapShotList)
          }


        })

        // 折线图设备信息的获取和配置图像
        getdataDeviceList(this.selected).then(response=>{

          // 一小时的设备数据
          if(this.selected == '1'){
            this.deviceList=[]
            let now = new Date()
            // 当前时间到间隔10分钟的一小时之前
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let tenMins=moment(now).subtract(10,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let twentyMins=moment(now).subtract(20,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let thirtyMins=moment(now).subtract(30,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let fourtyMins=moment(now).subtract(40,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let fiftyMins=moment(now).subtract(50,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let sixtyMins=moment(now).subtract(60,'minutes').format('YYYY-MM-DD HH:mm:ss')

            for(let i in response.data){
              if(response.data[i].createTime>=sixtyMins&&response.data[i].createTime<fiftyMins){
                one.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=fiftyMins&&response.data[i].createTime<fourtyMins){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=fourtyMins&&response.data[i].createTime<thirtyMins){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=thirtyMins&&response.data[i].createTime<twentyMins){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=twentyMins&&response.data[i].createTime<tenMins){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=tenMins&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0

            if(one.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(two.length ==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.deviceList.push(0)
            }
            else {
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            // console.log(this.deviceList)

            getAcquisitionTransmission(this.selected).then(response=>{


              if(response.data[0]>1024){
                this.title_collect='GB'
                response.data[0]=(parseFloat(response.data[0])/1024).toFixed(2)
                response.data[1]=(parseFloat(response.data[1])/1024).toFixed(2)
                response.data[2]=(parseFloat(response.data[2])/1024).toFixed(2)
              }
              else{
                this.title_collect='MB'
                response.data[0]=parseFloat(response.data[0]).toFixed(2)
                response.data[1]=parseFloat(response.data[1]).toFixed(2)
                response.data[2]=parseFloat(response.data[2]).toFixed(2)
              }
              if(response.data[3]>1024){
                this.title_transform='GB'
                response.data[3]=(parseFloat(response.data[3])/1024).toFixed(2)
                response.data[4]=(parseFloat(response.data[4])/1024).toFixed(2)
                response.data[5]=(parseFloat(response.data[5])/1024).toFixed(2)
              }
              else{
                this.title_transform='MB'
                response.data[3]=parseFloat(response.data[3]).toFixed(2)
                response.data[4]=parseFloat(response.data[4]).toFixed(2)
                response.data[5]=parseFloat(response.data[5]).toFixed(2)
              }

              // 第一个图表配置
              let first = this.$echarts.init(document.getElementById('data_num'))
              first.clear()
              first.setOption({
                title: {
                  text: '采集数据量 '+response.data[0]+this.title_collect,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item',
                  transitionDuration:0
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    hoverAnimation:true,
                    data: [
                      { value: response.data[1], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[2], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 第二个图表配置
              let second = this.$echarts.init(document.getElementById('data_transition'))
              second.clear()
              second.setOption({
                title: {
                  text: '传输数据量 '+response.data[3]+this.title_transform,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item',
                  transitionDuration:0
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    hoverAnimation:true,
                    radius: '50%',
                    data: [
                      { value: response.data[4], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[5], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      },
                      label:{
                        show:true
                      }
                    }
                  }
                ]
              })
              // 折线图配置
              let third = this.$echarts.init(document.getElementById('average_speed'))
              third.clear()
              third.setOption({
                title: {
                  text: '平均传输速率',
                  top:'10',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },

                tooltip: {
                  trigger: 'axis',
                  transitionDuration:0
                },
                grid: {
                  left: '3%',
                  right: '-3%',
                  bottom: '2%',
                  top:'27%',
                  containLabel: true
                },
                xAxis: {
                  type: 'category',
                  boundaryGap: true,
                  data: this.xValue,
                  axisLabel: {
                    padding: [0, 0, 0, -70],
                    inside: false,
                    // rotate:40,
                    textStyle: {
                      color: '#b2b2b2',
                      fontSize: '10',
                      itemSize: ''
                    }
                  },
                  axisLine:{
                    lineStyle:{
                      color:'#b2b2b2',
                    }
                  }
                },
                yAxis: {
                  type: 'value',
                  name:this.title_transform+'/时',
                  axisLine: {
                    show: false,//不显示坐标轴轴线
                    lineStyle:{
                      color:'#b2b2b2',
                      fontSize:'10'
                    }
                  },
                  axisTick: {
                    show: false  //不显示坐标轴刻度
                  },
                },
                series: [
                  {
                    name: '设备数据',
                    type: 'line',
                    data: this.deviceList,
                    itemStyle : {
                      normal : {
                        color:'#5894ff',
                        lineStyle:{
                          color: "#5894ff"
                        }
                      }
                    },
                  },
                  {
                    name: '快照数据',
                    type: 'line',
                    data: this.snapShotList,
                    itemStyle: {
                      color:'#ffbe46',
                      normal:{
                        color: "#ffbe46"
                      }
                    }
                  }
                ],
                animationDuration:3000,
                animationEasing:"cubicInOut"
              })
            })

          }
          // 一天的设备数据
          if(this.selected == '2'){
            this.deviceList=[]
            let now = new Date()
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let hours4=moment(now).subtract(4,'hours').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let hours8=moment(now).subtract(8,'hours').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let hours12=moment(now).subtract(12,'hours').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let hours16=moment(now).subtract(16,'hours').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let hours20=moment(now).subtract(20,'hours').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let hours24=moment(now).subtract(24,'hours').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断

            for(let i in response.data){
              if(response.data[i].createTime>=hours24&&response.data[i].createTime<hours20){
                one.push(response.data[i].characterLength)
              }
              if(response.data[i].createTime>=hours20&&response.data[i].createTime<hours16){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours16&&response.data[i].createTime<hours12){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours12&&response.data[i].createTime<hours8){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours8&&response.data[i].createTime<hours4){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours4&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0
            if(one.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              console.log('sum')
              console.log(sum)
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            console.log(this.deviceList)

            getAcquisitionTransmission(this.selected).then(response=>{

              if(response.data[0]>1024){
                this.title_collect='GB'
                response.data[0]=(parseFloat(response.data[0])/1024).toFixed(2)
                response.data[1]=(parseFloat(response.data[1])/1024).toFixed(2)
                response.data[2]=(parseFloat(response.data[2])/1024).toFixed(2)
              }
              else{
                this.title_collect='MB'
                response.data[0]=parseFloat(response.data[0]).toFixed(2)
                response.data[1]=parseFloat(response.data[1]).toFixed(2)
                response.data[2]=parseFloat(response.data[2]).toFixed(2)
              }
              if(response.data[3]>1024){
                this.title_transform='GB'
                response.data[3]=(parseFloat(response.data[3])/1024).toFixed(2)
                response.data[4]=(parseFloat(response.data[4])/1024).toFixed(2)
                response.data[5]=(parseFloat(response.data[5])/1024).toFixed(2)
              }
              else{
                this.title_transform='MB'
                response.data[3]=parseFloat(response.data[3]).toFixed(2)
                response.data[4]=parseFloat(response.data[4]).toFixed(2)
                response.data[5]=parseFloat(response.data[5]).toFixed(2)
              }

              // 第一个图表配置
              let first = this.$echarts.init(document.getElementById('data_num'))
              first.clear()
              first.setOption({
                title: {
                  text: '采集数据量 '+response.data[0]+this.title_collect,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[1], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[2], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 第二个图表配置
              let second = this.$echarts.init(document.getElementById('data_transition'))
              second.clear()
              second.setOption({
                title: {
                  text: '传输数据量 '+response.data[3]+this.title_transform,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[4], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[5], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 折线图配置
              let third = this.$echarts.init(document.getElementById('average_speed'))
              third.clear()
              third.setOption({
                title: {
                  text: '平均传输速率',
                  top:'10',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'axis'
                },
                grid: {
                  left: '3%',
                  right: '-3%',
                  top:'27%',
                  bottom: '2%',
                  containLabel: true
                },
                xAxis: {
                  type: 'category',
                  boundaryGap: true,
                  data: this.xValue,
                  axisLabel: {
                    // rotate:40,
                    padding: [0, 0, 0, -70],
                    inside: false,
                    textStyle: {
                      color: '#b2b2b2',
                      fontSize: '10',
                      itemSize: ''
                    }
                  },
                  axisLine:{
                    lineStyle:{
                      color:'#b2b2b2',
                    }
                  }
                },
                yAxis: {
                  type: 'value',
                  name:this.title_transform+'/时',
                  axisLine: {
                    show: false,//不显示坐标轴轴线
                    lineStyle:{
                      color:'#b2b2b2',
                      fontSize:'10'
                    }
                  },
                  axisTick: {
                    show: false  //不显示坐标轴刻度
                  },
                },
                series: [
                  {
                    name: '设备数据',
                    type: 'line',
                    data: this.deviceList,
                    itemStyle : {
                      normal : {
                        color:'#5894ff',
                        lineStyle:{
                          color: "#5894ff"
                        }
                      }
                    },
                  },
                  {
                    name: '快照数据',
                    type: 'line',
                    data: this.snapShotList,
                    itemStyle: {
                      color:'#ffbe46',
                      normal:{
                        color: "#ffbe46"
                      }
                    }
                  }
                ],
                animationDuration:3000,
                animationEasing:"cubicInOut"
              })
            })
          }
          // 一个月的设备数据
          if(this.selected == '3'){
            this.deviceList=[]
            let now = new Date()
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let day5=moment(now).subtract(5,'days').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let day10=moment(now).subtract(10,'days').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let day15=moment(now).subtract(15,'days').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let day20=moment(now).subtract(20,'days').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let day25=moment(now).subtract(25,'days').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let day30=moment(now).subtract(30,'days').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断

            for(let i in response.data){
              if(response.data[i].createTime>=day30&&response.data[i].createTime<day25){
                one.push(response.data[i].characterLength)
              }
              if(response.data[i].createTime>=day25&&response.data[i].createTime<day20){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day20&&response.data[i].createTime<day15){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day15&&response.data[i].createTime<day10){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day10&&response.data[i].createTime<day5){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day5&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0

            if(one.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            // console.log(this.deviceList)

            getAcquisitionTransmission(this.selected).then(response=>{

              if(response.data[0]>1024){
                this.title_collect='GB'
                response.data[0]=(parseFloat(response.data[0])/1024).toFixed(2)
                response.data[1]=(parseFloat(response.data[1])/1024).toFixed(2)
                response.data[2]=(parseFloat(response.data[2])/1024).toFixed(2)
              }
              else{
                this.title_collect='MB'
                response.data[0]=parseFloat(response.data[0]).toFixed(2)
                response.data[1]=parseFloat(response.data[1]).toFixed(2)
                response.data[2]=parseFloat(response.data[2]).toFixed(2)
              }
              if(response.data[3]>1024){
                this.title_transform='GB'
                response.data[3]=(parseFloat(response.data[3])/1024).toFixed(2)
                response.data[4]=(parseFloat(response.data[4])/1024).toFixed(2)
                response.data[5]=(parseFloat(response.data[5])/1024).toFixed(2)
              }
              else{
                this.title_transform='MB'
                response.data[3]=parseFloat(response.data[3]).toFixed(2)
                response.data[4]=parseFloat(response.data[4]).toFixed(2)
                response.data[5]=parseFloat(response.data[5]).toFixed(2)
              }

              // 第一个图表配置
              let first = this.$echarts.init(document.getElementById('data_num'))
              first.clear()
              first.setOption({
                title: {
                  text: '采集数据量 '+response.data[0]+this.title_collect,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[1], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[2], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 第二个图表配置
              let second = this.$echarts.init(document.getElementById('data_transition'))
              second.clear()
              second.setOption({
                title: {
                  text: '传输数据量 '+response.data[3]+this.title_transform,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[4], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[5], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 折线图配置
              let third = this.$echarts.init(document.getElementById('average_speed'))
              third.clear()
              third.setOption({
                title: {
                  text: '平均传输速率',
                  top:'10',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'axis'
                },
                grid: {
                  left: '3%',
                  right: '-3%',
                  top:'27%',
                  bottom: '2%',
                  containLabel: true
                },
                xAxis: {
                  type: 'category',
                  boundaryGap: true,
                  data: this.xValue,
                  axisLabel: {
                    // rotate:40,
                    padding: [0, 0, 0, -70],
                    inside: false,
                    textStyle: {
                      color: '#b2b2b2',
                      fontSize: '10',
                      itemSize: ''
                    }
                  },
                  axisLine:{
                    lineStyle:{
                      color:'#b2b2b2',
                    }
                  }
                },
                yAxis: {
                  type: 'value',
                  name:this.title_transform+'/天',
                  axisLine: {
                    show: false,//不显示坐标轴轴线
                    lineStyle:{
                      color:'#b2b2b2',
                      fontSize:'10'
                    }
                  },
                  axisTick: {
                    show: false  //不显示坐标轴刻度
                  },
                },
                series: [
                  {
                    name: '设备数据',
                    type: 'line',
                    data: this.deviceList,
                    itemStyle : {
                      normal : {
                        color:'#5894ff',
                        lineStyle:{
                          color: "#5894ff"
                        }
                      }
                    },
                  },
                  {
                    name: '快照数据',
                    type: 'line',
                    data: this.snapShotList,
                    itemStyle: {
                      color:'#ffbe46',
                      normal:{
                        color: "#ffbe46"
                      }
                    }
                  }
                ],
                animationDuration:3000,
                animationEasing:"cubicInOut"
              })
            })
          }

        })

      },
      reload(){
        // 根据参数设置不同的X轴刻度值
        let now = new Date()
        let tenMins=moment(now).subtract(10,'minutes').format('HH时mm分')
        let twentyMins=moment(now).subtract(20,'minutes').format('HH时mm分')
        let thirtyMins=moment(now).subtract(30,'minutes').format('HH时mm分')
        let fourtyMins=moment(now).subtract(40,'minutes').format('HH时mm分')
        let fiftyMins=moment(now).subtract(50,'minutes').format('HH时mm分')
        let sixtyMins=moment(now).subtract(60,'minutes').format('HH时mm分')
        let hour4=moment(now).subtract(4,'hours').format('DD日HH时')
        let hour8=moment(now).subtract(8,'hours').format('DD日HH时')
        let hour12=moment(now).subtract(12,'hours').format('DD日HH时')
        let hour16=moment(now).subtract(16,'hours').format('DD日HH时')
        let hour20=moment(now).subtract(20,'hours').format('DD日HH时')
        let hour24=moment(now).subtract(24,'hours').format('DD日HH时')
        let day5=moment(now).subtract(5,'days').format('MM月DD日')
        let day10=moment(now).subtract(10,'days').format('MM月DD日')
        let day15=moment(now).subtract(15,'days').format('MM月DD日')
        let day20=moment(now).subtract(20,'days').format('MM月DD日')
        let day25=moment(now).subtract(25,'days').format('MM月DD日')
        let day30=moment(now).subtract(30,'days').format('MM月DD日')
        if(this.selected == '1'){
          let nowTime=moment(now).format('HH时mm分')
          this.xValue=[sixtyMins,fiftyMins,fourtyMins,thirtyMins,twentyMins,tenMins,nowTime]
        }
        if(this.selected=='2'){
          let nowTime=moment(now).format('DD日HH时')
          this.xValue=[hour24,hour20,hour16,hour12,hour8,hour4,nowTime]
        }
        if(this.selected =='3'){
          let nowTime=moment(now).format('MM月DD日')
          this.xValue=[day30,day25,day20,day15,day10,day5,nowTime]
        }

        // 折线图快照信息的获取
        getdataSnapshotList(this.selected).then(response=>{
          // 最近一小时的快照数据
          if(this.selected =='1'){
            this.snapShotList=[]
            let now = new Date()
            // 当前时间到间隔10分钟的一小时之前
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let tenMins=moment(now).subtract(10,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let twentyMins=moment(now).subtract(20,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let thirtyMins=moment(now).subtract(30,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let fourtyMins=moment(now).subtract(40,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let fiftyMins=moment(now).subtract(50,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let sixtyMins=moment(now).subtract(60,'minutes').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断

            for(let i in response.data){
              if(response.data[i].createTime>=sixtyMins&&response.data[i].createTime<fiftyMins){
                one.push(response.data[i].characterLength)
              }
              if(response.data[i].createTime>=fiftyMins&&response.data[i].createTime<fourtyMins){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=fourtyMins&&response.data[i].createTime<thirtyMins){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=thirtyMins&&response.data[i].createTime<twentyMins){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=twentyMins&&response.data[i].createTime<tenMins){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=tenMins&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0

            if(one.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.snapShotList.push((sum/10).toFixed(5))
              sum=0
            }

            // console.log(this.snapShotList)
          }

          // 最近一天的快照数据
          if(this.selected =='2'){
            this.snapShotList=[]
            let now = new Date()
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let hour4=moment(now).subtract(4,'hours').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let hour8=moment(now).subtract(8,'hours').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let hour12=moment(now).subtract(12,'hours').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let hour16=moment(now).subtract(16,'hours').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let hour20=moment(now).subtract(20,'hours').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let hour24=moment(now).subtract(24,'hours').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断
            for(let i in response.data){
              if(response.data[i].createTime>=hour24&&response.data[i].createTime<hour20){
                one.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour20&&response.data[i].createTime<hour16){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour16&&response.data[i].createTime<hour12){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour12&&response.data[i].createTime<hour8){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour8&&response.data[i].createTime<hour4){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hour4&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum =0
            if(one.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }


            if(two.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.snapShotList.push(0)
            }
            else {
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.snapShotList.push((sum/4).toFixed(5))
              sum=0
            }
            console.log(this.snapShotList)
          }

          // // 最近一个月的快照数据
          if(this.selected=='3'){
            this.snapShotList=[]
            let now = new Date()
            // 一个月之间的间隔点
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let day5=moment(now).subtract(5,'days').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let day10=moment(now).subtract(10,'days').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let day15=moment(now).subtract(15,'days').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let day20=moment(now).subtract(20,'days').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let day25=moment(now).subtract(25,'days').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let day30=moment(now).subtract(30,'days').format('YYYY-MM-DD HH:mm:ss')



            // 对时间进行判断
            for(let i in response.data){
              if(response.data[i].createTime>=day30&&response.data[i].createTime<day25){
                one.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day25&&response.data[i].createTime<day20){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day20&&response.data[i].createTime<day15){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day15&&response.data[i].createTime<day10){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day10&&response.data[i].createTime<day5){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day5&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0
            if(one.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.snapShotList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.snapShotList.push(0)
            }
            else {
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.snapShotList.push((sum/5).toFixed(5))
              sum=0
            }

            // console.log(this.snapShotList)
          }

        })
        // 折线图设备信息的获取和配置图像
        getdataDeviceList(this.selected).then(response=>{
          // 一小时的设备数据
          if(this.selected == '1'){
            this.deviceList=[]
            let now = new Date()
            // 当前时间到间隔10分钟的一小时之前
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let tenMins=moment(now).subtract(10,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let twentyMins=moment(now).subtract(20,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let thirtyMins=moment(now).subtract(30,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let fourtyMins=moment(now).subtract(40,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let fiftyMins=moment(now).subtract(50,'minutes').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let sixtyMins=moment(now).subtract(60,'minutes').format('YYYY-MM-DD HH:mm:ss')

            for(let i in response.data){
              if(response.data[i].createTime>=sixtyMins&&response.data[i].createTime<fiftyMins){
                one.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=fiftyMins&&response.data[i].createTime<fourtyMins){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=fourtyMins&&response.data[i].createTime<thirtyMins){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=thirtyMins&&response.data[i].createTime<twentyMins){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=twentyMins&&response.data[i].createTime<tenMins){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=tenMins&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0

            if(one.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(two.length ==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.deviceList.push(0)
            }
            else {
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.deviceList.push((sum/10).toFixed(5))
              sum=0
            }

            // 获取饼图的数据
            getAcquisitionTransmission(this.selected).then(response=>{

              if(response.data[0]>1024){
                this.title_collect='GB'
                response.data[0]=(parseFloat(response.data[0])/1024).toFixed(2)
                response.data[1]=(parseFloat(response.data[1])/1024).toFixed(2)
                response.data[2]=(parseFloat(response.data[2])/1024).toFixed(2)
              }
              else{
                this.title_collect='MB'
                response.data[0]=parseFloat(response.data[0]).toFixed(2)
                response.data[1]=parseFloat(response.data[1]).toFixed(2)
                response.data[2]=parseFloat(response.data[2]).toFixed(2)
              }
              if(response.data[3]>1024){
                this.title_transform='GB'
                response.data[3]=(parseFloat(response.data[3])/1024).toFixed(2)
                response.data[4]=(parseFloat(response.data[4])/1024).toFixed(2)
                response.data[5]=(parseFloat(response.data[5])/1024).toFixed(2)
              }
              else{
                this.title_transform='MB'
                response.data[3]=parseFloat(response.data[3]).toFixed(2)
                response.data[4]=parseFloat(response.data[4]).toFixed(2)
                response.data[5]=parseFloat(response.data[5]).toFixed(2)
              }

              // 第一个图表配置
              let first = this.$echarts.init(document.getElementById('data_num'))
              first.clear()
              first.setOption({
                title: {
                  text: '采集数据量 '+response.data[0]+this.title_collect,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item',
                  transitionDuration:0
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    hoverAnimation:true,
                    data: [
                      { value: response.data[1], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[2], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 第二个图表配置
              let second = this.$echarts.init(document.getElementById('data_transition'))
              second.clear()
              second.setOption({
                title: {
                  text: '传输数据量 '+response.data[3]+this.title_transform,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item',
                  transitionDuration:0
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    hoverAnimation:true,
                    radius: '50%',
                    data: [
                      { value: response.data[4], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[5], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      },
                      label:{
                        show:true
                      }
                    }
                  }
                ]
              })
              // 折线图配置
              let third = this.$echarts.init(document.getElementById('average_speed'))
              third.clear()
              third.setOption({
                title: {
                  text: '平均传输速率',
                  top:'10',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'axis',
                  transitionDuration:0
                },

                grid: {
                  left: '3%',
                  right: '-3%',
                  top:'27%',
                  bottom: '2%',
                  containLabel: true
                },
                xAxis: {
                  type: 'category',
                  boundaryGap: true,
                  data: this.xValue,
                  axisLabel: {
                    // rotate:40,
                    padding: [0, 0, 0, -70],
                    inside: false,
                    textStyle: {
                      color: '#b2b2b2',
                      fontSize: '10',
                      itemSize: ''
                    }
                  },
                  axisLine:{
                    lineStyle:{
                      color:'#b2b2b2',
                    }
                  }
                },
                yAxis: {
                  type: 'value',
                  name:this.title_transform+'/分',
                  axisLine: {
                    show: false ,//不显示坐标轴轴线
                    lineStyle:{
                      color:'#b2b2b2',
                      fontSize:'10'
                    }
                  },
                  axisTick: {
                    show: false  //不显示坐标轴刻度
                  },
                },
                series: [
                  {
                    name: '设备数据',
                    type: 'line',
                    data: this.deviceList,
                    itemStyle : {
                      normal : {
                        color:'#5894ff',
                        lineStyle:{
                          color: "#5894ff"
                        }
                      }
                    },
                  },
                  {
                    name: '快照数据',
                    type: 'line',
                    data: this.snapShotList,
                    itemStyle: {
                      color:'#ffbe46',
                      normal:{
                        color: "#ffbe46"
                      }
                    }
                  }
                ],
                animationDuration:3000,
                animationEasing:"cubicInOut"
              })
            })

          }
          // 一天的设备数据
          if(this.selected == '2'){
            this.deviceList=[]
            let now = new Date()
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let hours4=moment(now).subtract(4,'hours').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let hours8=moment(now).subtract(8,'hours').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let hours12=moment(now).subtract(12,'hours').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let hours16=moment(now).subtract(16,'hours').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let hours20=moment(now).subtract(20,'hours').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let hours24=moment(now).subtract(24,'hours').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断

            for(let i in response.data){
              if(response.data[i].createTime>=hours24&&response.data[i].createTime<hours20){
                one.push(response.data[i].characterLength)
              }
              if(response.data[i].createTime>=hours20&&response.data[i].createTime<hours16){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours16&&response.data[i].createTime<hours12){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours12&&response.data[i].createTime<hours8){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours8&&response.data[i].createTime<hours4){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=hours4&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0

            if(one.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.deviceList.push((sum/4).toFixed(5))
              sum=0
            }

            // console.log(this.deviceList)

            getAcquisitionTransmission(this.selected).then(response=>{

              if(response.data[0]>1024){
                this.title_collect='GB'
                response.data[0]=(parseFloat(response.data[0])/1024).toFixed(2)
                response.data[1]=(parseFloat(response.data[1])/1024).toFixed(2)
                response.data[2]=(parseFloat(response.data[2])/1024).toFixed(2)
              }
              else{
                this.title_collect='MB'
                response.data[0]=parseFloat(response.data[0]).toFixed(2)
                response.data[1]=parseFloat(response.data[1]).toFixed(2)
                response.data[2]=parseFloat(response.data[2]).toFixed(2)
              }
              if(response.data[3]>1024){
                this.title_transform='GB'
                response.data[3]=(parseFloat(response.data[3])/1024).toFixed(2)
                response.data[4]=(parseFloat(response.data[4])/1024).toFixed(2)
                response.data[5]=(parseFloat(response.data[5])/1024).toFixed(2)
              }
              else{
                this.title_transform='MB'
                response.data[3]=parseFloat(response.data[3]).toFixed(2)
                response.data[4]=parseFloat(response.data[4]).toFixed(2)
                response.data[5]=parseFloat(response.data[5]).toFixed(2)
              }

              // 第一个图表配置
              let first = this.$echarts.init(document.getElementById('data_num'))
              first.clear()
              first.setOption({
                title: {
                  text: '采集数据量 '+response.data[0]+this.title_collect,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[1], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[2], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 第二个图表配置
              let second = this.$echarts.init(document.getElementById('data_transition'))
              second.clear()
              second.setOption({
                title: {
                  text: '传输数据量 '+response.data[3]+this.title_transform,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[4], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[5], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 折线图配置
              let third = this.$echarts.init(document.getElementById('average_speed'))
              third.clear()
              third.setOption({
                title: {
                  text: '平均传输速率',
                  top:'10',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'axis'
                },

                grid: {
                  left: '3%',
                  right: '-3%',
                  top:'27%',
                  bottom: '2%',
                  containLabel: true
                },
                xAxis: {
                  type: 'category',
                  boundaryGap: true,
                  data: this.xValue,
                  axisLabel: {
                    // rotate:40,
                    padding: [0, 0, 0, -70],
                    inside: false,
                    textStyle: {
                      color: '#b2b2b2',
                      fontSize: '10',
                      itemSize: ''
                    }
                  },
                  axisLine:{
                    lineStyle:{
                      color:'#b2b2b2',
                    }
                  }
                },
                yAxis: {
                  type: 'value',
                  name:this.title_transform+'/时',
                  axisLine: {
                    show: false,//不显示坐标轴轴线
                    lineStyle:{
                      color:'#b2b2b2',
                      fontSize:'10'
                    }
                  },
                  axisTick: {
                    show: false  //不显示坐标轴刻度
                  },
                },
                series: [
                  {
                    name: '设备数据',
                    type: 'line',
                    data: this.deviceList,
                    itemStyle : {
                      normal : {
                        color:'#5894ff',
                        lineStyle:{
                          color: "#5894ff"
                        }
                      }
                    },
                  },
                  {
                    name: '快照数据',
                    type: 'line',
                    data: this.snapShotList,
                    itemStyle: {
                      color:'#ffbe46',
                      normal:{
                        color: "#ffbe46"
                      }
                    }
                  }
                ],
                animationDuration:3000,
                animationEasing:"cubicInOut"
              })
            })
          }
          // 一个月的设备数据
          if(this.selected == '3'){
            this.deviceList=[]
            let now = new Date()
            let nowTime=moment(now).format('YYYY-MM-DD HH:mm:ss')
            let  six=[]
            let day5=moment(now).subtract(5,'days').format('YYYY-MM-DD HH:mm:ss')
            let five=[]
            let day10=moment(now).subtract(10,'days').format('YYYY-MM-DD HH:mm:ss')
            let four=[]
            let day15=moment(now).subtract(15,'days').format('YYYY-MM-DD HH:mm:ss')
            let three=[]
            let day20=moment(now).subtract(20,'days').format('YYYY-MM-DD HH:mm:ss')
            let two=[]
            let day25=moment(now).subtract(25,'days').format('YYYY-MM-DD HH:mm:ss')
            let one=[]
            let day30=moment(now).subtract(30,'days').format('YYYY-MM-DD HH:mm:ss')
            // 对时间进行判断

            for(let i in response.data){
              if(response.data[i].createTime>=day30&&response.data[i].createTime<day25){
                one.push(response.data[i].characterLength)
              }
              if(response.data[i].createTime>=day25&&response.data[i].createTime<day20){
                two.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day20&&response.data[i].createTime<day15){
                three.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day15&&response.data[i].createTime<day10){
                four.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day10&&response.data[i].createTime<day5){
                five.push(parseFloat(response.data[i].characterLength))
              }
              if(response.data[i].createTime>=day5&&response.data[i].createTime<nowTime){
                six.push(parseFloat(response.data[i].characterLength))
              }
            }

            let sum=0

            if(one.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in one){
                sum+=parseFloat(one[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(two.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in two){
                sum+=parseFloat(two[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(three.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in three){
                sum+=parseFloat(three[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(four.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in four){
                sum+=parseFloat(four[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(five.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in five){
                sum+=parseFloat(five[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            if(six.length==0){
              this.deviceList.push(0)
            }
            else{
              for(let i in six){
                sum+=parseFloat(six[i])
              }
              this.deviceList.push((sum/5).toFixed(5))
              sum=0
            }

            // console.log(this.deviceList)

            getAcquisitionTransmission(this.selected).then(response=>{

              if(response.data[0]>1024){
                this.title_collect='GB'
                response.data[0]=(parseFloat(response.data[0])/1024).toFixed(2)
                response.data[1]=(parseFloat(response.data[1])/1024).toFixed(2)
                response.data[2]=(parseFloat(response.data[2])/1024).toFixed(2)
              }
              else{
                this.title_collect='MB'
                response.data[0]=parseFloat(response.data[0]).toFixed(2)
                response.data[1]=parseFloat(response.data[1]).toFixed(2)
                response.data[2]=parseFloat(response.data[2]).toFixed(2)
              }
              if(response.data[3]>1024){
                this.title_transform='GB'
                response.data[3]=(parseFloat(response.data[3])/1024).toFixed(2)
                response.data[4]=(parseFloat(response.data[4])/1024).toFixed(2)
                response.data[5]=(parseFloat(response.data[5])/1024).toFixed(2)
              }
              else{
                this.title_transform='MB'
                response.data[3]=parseFloat(response.data[3]).toFixed(2)
                response.data[4]=parseFloat(response.data[4]).toFixed(2)
                response.data[5]=parseFloat(response.data[5]).toFixed(2)
              }
              // 第一个图表配置
              let first = this.$echarts.init(document.getElementById('data_num'))
              first.clear()
              first.setOption({
                title: {
                  text: '采集数据量 '+response.data[0]+this.title_collect,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[1], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[2], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 第二个图表配置
              let second = this.$echarts.init(document.getElementById('data_transition'))
              second.clear()
              second.setOption({
                title: {
                  text: '传输数据量 '+response.data[3]+this.title_transform,
                  left: 'center',
                  top:'10px',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'item'
                },
                legend:{
                  bottom:0,
                  left:'25%',
                  itemHeight: 12,
                  itemWidth: 12,
                  data: [
                    {
                      name: '快照数据',
                      icon: 'circle',
                    },
                    {
                      name: '设备数据',
                      icon: 'circle',
                    }
                  ]
                },
                series: [
                  {
                    type: 'pie',
                    radius: '50%',
                    data: [
                      { value: response.data[4], name: '设备数据',itemStyle: {
                          normal:{
                            color: "#5894ff"
                          }
                        } },
                      { value: response.data[5], name: '快照数据',itemStyle: {
                          normal:{
                            color: "#ffbe46"
                          }
                        } }
                    ],
                    emphasis: {
                      itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                      }
                    }
                  }
                ]
              })
              // 折线图配置
              let third = this.$echarts.init(document.getElementById('average_speed'))
              third.clear()
              third.setOption({
                title: {
                  text: '平均传输速率',
                  top:'10',
                  textStyle: {
                    fontSize:'14',
                    color: "#444444"
                  },
                },
                tooltip: {
                  trigger: 'axis'
                },
                grid: {
                  left: '3%',
                  right: '-3%',
                  top:'27%',
                  bottom: '2%',

                  containLabel: true
                },
                xAxis: {
                  type: 'category',
                  boundaryGap: true,
                  data: this.xValue,
                  axisLabel: {
                    padding: [0, 0, 0, -70],
                    // 存在rotate时，padding和align无效
                    // rotate:40,
                    inside: false,
                    textStyle: {
                      color: '#b2b2b2',
                      fontSize: '10',
                      itemSize: ''
                    }
                  },
                  axisLine:{
                    lineStyle:{
                      color:'#b2b2b2',
                    }
                  }
                },
                yAxis: {
                  type: 'value',
                  name:this.title_transform+'/天',

                  axisLine: {
                    show: false,  //不显示坐标轴轴线
                    lineStyle:{
                      color:'#b2b2b2',
                      fontSize:'10'
                    }
                  },
                  axisTick: {
                    show: false  //不显示坐标轴刻度
                  },
                },
                series: [
                  {
                    name: '设备数据',
                    type: 'line',
                    data: this.deviceList,
                    itemStyle : {
                      normal : {
                        color:'#5894ff',
                        lineStyle:{
                          color: "#5894ff"
                        }
                      }
                    },
                  },
                  {
                    name: '快照数据',
                    type: 'line',
                    data: this.snapShotList,
                    itemStyle: {
                      color:'#ffbe46',
                      normal:{
                        color: "#ffbe46"
                      }
                    }
                  }
                ],
                animationDuration:3000,
                animationEasing:"cubicInOut"
              })
            })
          }
        })


      }
    }
  };

</script>

<style scoped>
  .app-container{
    padding: 0;
  }
  .title_line{
    width: 4px;
    display: inline-block;
    height: 14px;
    margin: auto;
    background-color: #5894ff;
    position: relative;
    margin-right: 10px;
  }
  .title-text{
    font-size: 16px;
    font-weight: bold;
  }
  .situation_title{
    height: 24px;
    font-size: 16px;
    line-height:24px;
    padding-left: 24px;
    margin-bottom:20px;
  }
  .situation_title img{
    width: 4px;
    height: 18px;
    vertical-align: middle;
    display: inline-block;
    margin-top: -3px;
  }
  #son_1{
    background: url("../assets/icons/img/sheibeiBack.png");
    font-size: 14px;
  }
  #son_2{
    background: url("../assets/icons/img/canshuBack.png");
    font-size: 14px;
  }
  #son_3{
    background: url("../assets/icons/img/cemeraBack.png");
    font-size: 14px;
  }
  .main{
    display: flex;
    justify-content: space-between;
    padding-left: 20px;
    padding-right: 20px;
  }
  .main-left{
    width: 65.5%;
  }
  .main-right{
    width: 31%;
  }

  .transition{
    margin-top: 30px;
  }


  /*设备信息样式*/
  .info{
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    background: #5894ff;
    border-radius: 5px;
  }
  .info>div{
    width: 100%;
    font-size: 18px;
    height: 18px;
    line-height: 18px;
    padding-left: 24px;
    color: #FFFFFF;
    font-size: 16px;

  }
  .info-left {
    width: 100%;
    text-align: center;
    box-sizing: border-box;
    margin: 0;
  }
  .info-bottom span{
    vertical-align: top;
    margin-right: 50px;
    margin-left: 20px;
  }
  .situation{
    margin-top: 20px;
    margin-bottom: 30px;
    box-sizing: border-box;
  }


  el-progress{
    height: 20px;
    width: 20px;
    margin-right: 30px;
  }
  .info-bottom{
    box-sizing: border-box;
    height: 100px;
    margin: 0;
    padding: 0;
    line-height: 100px;
    height: 160px;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
  }
  .info-right  div{
    margin-bottom: 20px;
  }
  .info-right div:first-child{
    margin-top: 5px;
  }

  /*接入情况样式*/
  .situation_num{
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    justify-content: space-around;
    height: 80px;
    line-height: 80px;
  }
  .situation_num_son{
    border-radius: 5px;
    width: 30%;
    height: 80px;
    line-height: 80px;
    padding-left: 24px;
    font-size: 16px;
    color: #FFFFFF;
  }
  .situation_num_son img{
    vertical-align: middle;
    margin-right: 12px;
  }
  .situation_num_son span{
    margin-left: 30px;
  }

  /*数据采集传输情况*/

  .e_box{
    display: flex;
    padding-top: 10px;
  }
  #data_transition{
    margin-left: 50px;
    margin-right: 50px;
  }

  .system-tooltip{
    width: 170px;
    height: 80px;
    font-size: 12px;
    color: #2e4773;
    border-color: #FFFFFF;
  }
  .system-tooltipLast{
    width: 170px;
    height: 40px;
    font-size: 16px;
    color: #2e4773;
  }

  /*谷歌隐藏滚动条*/
  ::-webkit-scrollbar {
    display: none;
  }
</style>
