import request from '@/utils/request'

// 查询采集模块配置列表
export function collectModuleList(query) {
  return request({
    url: '/collect/module/list',
    method: 'get',
    params: query
  })
}

//查询采集模块配置详细
export function getCollectModule(id) {
  return request({
    url: '/collect/module/'+ id,
    method: 'get'
  })
}

//修改采集模块配置
export function updateCollectModule(data) {
  return request({
    url:'/collect/module',
    method:'put',
    data:data
  })
}

//新增采集模块配置
export function addCollectModule(data) {
  return request({
    url:'/collect/module',
    method:'post',
    data:data
  })
}
//删除采集模块配置
export function delCollectModule(id) {
  return request({
    url:'/collect/module/'+ id,
    method: 'delete'
  })
}

// 查询参数列表
export function getAllSerialPort() {
  return request({
    url: '/device/getAllSerialPort',
    method: 'get'
  })
}

// 查询采集节点配置列表
export function collectNodeLists() {
  return request({
    url: '/collect/node/listNodes',
    method: 'get'
  })
}

// 查询wanip
export function getWanIp() {
  return request({
    url: '/collect/module/wanIp/getIp',
    method: 'get'
  })
}
