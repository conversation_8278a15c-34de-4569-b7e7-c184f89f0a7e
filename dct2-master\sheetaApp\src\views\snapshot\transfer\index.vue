<template>
  <div class="app-container" id="deviceDiv">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="通道名称" prop="name">
        <el-select v-model="queryParams.channelCode" placeholder="请选择快照通道" clearable @keyup.enter.native="handleQuery">
          <el-option
            v-for="channel in channelOptions"
            :key="channel.code"
            :label="channel.name"
            :value="channel.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!--<el-row :gutter="10" class="mb8">-->
      <!--&lt;!&ndash;<el-col :span="1.5">&ndash;&gt;-->
        <!--&lt;!&ndash;<el-button&ndash;&gt;-->
          <!--&lt;!&ndash;type="primary"&ndash;&gt;-->
          <!--&lt;!&ndash;icon="el-icon-plus"&ndash;&gt;-->
          <!--&lt;!&ndash;size="mini"&ndash;&gt;-->
          <!--&lt;!&ndash;@click="handleAdd"&ndash;&gt;-->
          <!--&lt;!&ndash;v-hasPermi="['snapshot:transfer:add']"&ndash;&gt;-->
        <!--&lt;!&ndash;&gt;新增&ndash;&gt;-->
        <!--&lt;!&ndash;</el-button>&ndash;&gt;-->
      <!--&lt;!&ndash;</el-col>&ndash;&gt;-->
      <!--<el-col :span="1.5">-->
        <!--<el-button-->
          <!--type="success"-->
          <!--icon="el-icon-edit"-->
          <!--size="mini"-->
          <!--:disabled="single"-->
          <!--@click="handleUpdate"-->
          <!--v-hasPermi="['snapshot:transfer:edit']"-->
        <!--&gt;修改-->
        <!--</el-button>-->
      <!--</el-col>-->
      <!--<el-col :span="1.5">-->
        <!--<el-button-->
          <!--type="danger"-->
          <!--icon="el-icon-delete"-->
          <!--size="mini"-->
          <!--:disabled="multiple"-->
          <!--@click="handleDelete"-->
          <!--v-hasPermi="['snapshot:transfer:remove']"-->
        <!--&gt;删除-->
        <!--</el-button>-->
      <!--</el-col>-->
      <!--<el-col :span="1.5">-->
        <!--<el-button-->
          <!--type="warning"-->
          <!--icon="el-icon-download"-->
          <!--size="mini"-->
          <!--@click="handleExport"-->
          <!--v-hasPermi="['snapshot:transfer:export']"-->
        <!--&gt;导出-->
        <!--</el-button>-->
      <!--</el-col>-->
    <!--</el-row>-->

    <el-table v-loading="loading" :data="transferList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="45" align="center"/>
      <el-table-column label="通道名称" align="center" prop="channelName" :show-overflow-tooltip="true"/>
      <el-table-column label="传输分辨率" align="center" prop="resolvingPower" :show-overflow-tooltip="true"/>
      <el-table-column label="传输间隔" align="center" prop="compartmentStr" :show-overflow-tooltip="true"/>
      <el-table-column label="优先级" align="center" prop="cost" :show-overflow-tooltip="true"/>
      <el-table-column label="连接状态" align="center" prop="connectStatusStr" :show-overflow-tooltip="true"/>
      <el-table-column label="预览" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="showChanelImg(scope.row)"
            v-hasPermi="['snapshot:transfer:edit']"
          >预览
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="存储状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="changeStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="传输状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.transferStatus"
            :active-value="1"
            :inactive-value="0"
            @change="changeTrStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <!--<el-table-column label="创建时间" align="center" prop="createTime" width="180">-->
        <!--<template slot-scope="scope">-->
          <!--<span>{{ parseTime(scope.row.createTime) }}</span>-->
        <!--</template>-->
      <!--</el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['snapshot:transfer:edit']"
          >修改
          </el-button>
          <!--<el-button-->
            <!--size="mini"-->
            <!--type="text"-->
            <!--icon="el-icon-delete"-->
            <!--@click="handleDelete(scope.row)"-->
            <!--v-hasPermi="['snapshot:transfer:remove']"-->
          <!--&gt;删除-->
          <!--</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!--快照弹出框-->
    <el-dialog :title="snapshotTitle" :visible.sync="snapshotOpen" width="800px" @close="snapshotCancel">
      <div>
        <img v-if="snapshotUrl" alt="快照" :src="snapshotUrl"/>
      </div>
    </el-dialog>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="快照通道" prop="mac">
          <el-select v-model="form.channelCode" placeholder="请选择快照通道" :disabled="updateAction">
            <el-option
              v-for="channel in channelOptions"
              :key="channel.code"
              :label="channel.name"
              :value="channel.code"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="分辨率" prop="resolvingPower">
          <el-select v-model="form.resolvingPower" placeholder="请选择分辨率">
            <el-option
              v-for="rp in resolvingPowerOptions"
              :key="rp"
              :label="rp"
              :value="rp"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="传输间隔" prop="compartment">
          <el-input v-model="form.compartment" placeholder="请输入传输间隔" v-show="false"/>
          <el-select v-model="compartmentNum">
            <el-option
              v-for="num in numOptions"
              :key="num"
              :label="num"
              :value="num"
            ></el-option>
          </el-select>
          <el-select v-model="compartmentUnit">
            <el-option
              v-for="unit in unitOptions"
              :key="unit.val"
              :label="unit.label"
              :value="unit.val"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="cost">
          <el-select v-model="form.cost" placeholder="请选择优先级" :value="40">
            <el-option
              v-for="co in costOptions"
              :key="co"
              :label="co"
              :value="co"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" align="center">
        <el-button
          size="mini"
          type="text"
          :loading="preLoading"
          @click="showPre(form.channelCode)"
        >预览
        </el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 预览 -->
    <el-dialog :title="预览" :visible="preview" width="650px" @close='closeDialog'>
      <el-image :src="address"></el-image>
    </el-dialog>
  </div>
</template>

<script>
  import {
    addTransfer,
    allChannelList,
    changeStatus,
    delTransfer,
    exportTransfer,
    getTransfer,
    transferList,
    unboundChannelList,
    updateTransfer,
    changeTrStatus,
    screenshot
  } from '@/api/snapshot/transfer'

  export default {
    name: 'SnapshotTransfer',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 参数表格数据
        transferList: [],
        address:'',
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          channelCode: undefined
        },
        updateAction: false,
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          resolvingPower: [
            {required: true, message: '分辨率不能为空', trigger: 'blur'}
          ],
          compartment: [
            {required: true, message: '传输间隔不能为空', trigger: 'blur'}
          ]
        },
        // 快照通道
        channelOptions: [],
        resolvingPowerOptions: ['320*180', '384*216', '480*270', '640*360', '768*432', '960*540', '1440*810', '1920*1080'],
        // 优先级
        costOptions: [10, 20, 30, 40, 50],
        // 传输间隔
        numOptions: [1, 2, 3, 4, 5, 6, 10, 12, 15, 20, 30],
        // 传输间隔
        unitOptions: [{val: 1, label: '秒'}, {val: 2, label: '分'}],
        compartmentNum: 5,
        compartmentUnit: 2,
        snapshotTitle: '',
        snapshotOpen: false,
        snapshotUrl: '',
        preview: false,
        preLoading: false
      }
    },
    created() {
      this.allChannelList();
      this.getList();
    },
    methods: {
      /** 查询通道列表 */
      getList() {
        var _this = this;
        _this.loading = true;
        transferList(_this.addDateRange(_this.queryParams, _this.dateRange)).then(response => {
            _this.transferList = response.rows;
            _this.total = response.total;
            _this.loading = false;
          }
        )
      },
      unboundChannelList() {
        var _this = this;
        _this.loading = true;
        unboundChannelList().then(response => {
            _this.channelOptions = response.data;
            _this.loading = false;
          }
        )
      },
      allChannelList() {
        var _this = this;
        _this.loading = true;
        allChannelList().then(response => {
            _this.channelOptions = response.data;
            _this.loading = false;
          }
        )
      },
      compartmentChange() {
        this.form.compartment = this.compartmentNum;
        if (this.compartmentUnit === 2) {
          this.form.compartment = this.compartmentNum * 60;
        }
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: undefined,
          channelCode: undefined,
          resolvingPower: '640*360',
          compartment: undefined,
          cost: 40
        };
        this.compartmentNum = 5;
        this.compartmentUnit = 2;
        this.resetForm('form');
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.unboundChannelList();
        this.open = true;
        this.title = '添加通道传输';
        this.updateAction = false;
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        var _this = this;
        _this.reset();
        _this.allChannelList();
        const id = row.id || _this.ids;
        getTransfer(id).then(response => {
          _this.form = response.data;
          _this.open = true;
          _this.title = '修改参数';
          _this.updateAction = true;

          if (_this.form.compartment >= 60) {
            _this.compartmentNum = _this.form.compartment / 60;
            _this.compartmentUnit = 2;
          } else {
            _this.compartmentNum = _this.form.compartment;
            _this.compartmentUnit = 1;
          }
        })
      },
      /** 提交按钮 */
      submitForm: function () {
        var _this = this;
        _this.compartmentChange();
        _this.$refs['form'].validate(valid => {
          if (valid) {
            if (_this.form.id !== undefined) {
              updateTransfer(_this.form).then(response => {
                if (response.code === 200) {
                  _this.msgSuccess('修改成功');
                  _this.open = false;
                  _this.getList();
                } else {
                  _this.msgError(response.msg);
                }
              })
            } else {
              addTransfer(_this.form).then(response => {
                if (response.code === 200) {
                  _this.msgSuccess('新增成功');
                  _this.open = false;
                  _this.getList();
                } else {
                  _this.msgError(response.msg);
                }
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var _this = this;
        const ids = row.id || _this.ids;
        _this.$confirm('是否确认删除选择的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return delTransfer(ids);
        }).then(() => {
          _this.getList();
          _this.msgSuccess('删除成功');
        }).catch(function () {
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        var _this = this;
        const queryParams = this.queryParams;
        _this.$confirm('是否确认导出所有数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return exportTransfer(queryParams);
        }).then(response => {
          _this.download(response.msg)
        }).catch(function () {
        })
      },
      /** 状态修改 */
      changeStatus(row) {
        let that = this;
        let text = row.status === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.channelName + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return changeStatus(row.id, row.status);
        }).then(() => {
          that.msgSuccess(text + '成功');
        }).catch(function () {
          row.status = row.status === 0 ? 1 : 0;
        });
      },
      /** 传输状态修改*/
      changeTrStatus(row) {
        let that = this;
        let text = row.transferStatus === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.channelName + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return changeTrStatus(row.id, row.transferStatus);
        }).then(() => {
          that.msgSuccess(text + '成功');
        }).catch(function () {
          row.transferStatus = row.transferStatus === 0 ? 1 : 0;
        });
      },
      showChanelImg(row) {
        this.snapshotTitle = row.fileName;
        this.snapshotOpen = true;
        this.snapshotUrl = row.directory + '/' + row.fileName;
      },
      /** 窗口关闭 */
      snapshotCancel() {
        this.snapshotOpen = false;
      },
      showPre(channelCode){
        var _this = this;
        _this.preLoading = true;
        screenshot(channelCode).then(response => {
          if (response.code === 200) {
            _this.address = response.msg;
          } else {
            _this.msgError(response.msg);
          }
        });
        setTimeout(() =>{
          _this.preLoading = false;
          _this.preview = true;
        },2000);
      },
      closeDialog(){
        this.preview = false;
      }
    }
  }
</script>

