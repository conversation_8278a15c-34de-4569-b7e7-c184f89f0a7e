import request from '@/utils/request'

// 查询传输管理列表
export function transferList(query) {
  return request({
    url: '/snapshot/transfer/list',
    method: 'get',
    params: query
  })
}

// 导出通道传输
export function exportTransfer(query) {
  return request({
    url: '/snapshot/transfer/export',
    method: 'get',
    params: query
  })
}

//查询传输管理详细
export function getTransfer(id) {
  return request({
    url: '/snapshot/transfer/' + id,
    method: 'get'
  })
}

//新增
export function addTransfer(data) {
  return request({
    url: '/snapshot/transfer',
    method: 'post',
    data: data
  });
}

//修改传输管理
export function updateTransfer(data) {
  return request({
    url: '/snapshot/transfer',
    method: 'put',
    data: data
  })
}

//删除
export function delTransfer(id) {
  return request({
    url: '/snapshot/transfer/' + id,
    method: 'delete'
  })
}

// 存储状态修改
export function changeStatus(id, status) {
  return request({
    url: '/snapshot/transfer/changeStatus/' + id + '/' + status,
    method: 'post'
  })
}

// 查询快照通道
export function unboundChannelList() {
  return request({
    url: '/snapshot/channel/unboundList',
    method: 'get'
  })
}
// 查询快照通道
export function allChannelList() {
  return request({
    url: '/snapshot/channel/allList',
    method: 'get'
  })
}

// 传输状态修改
export function changeTrStatus(id, status) {
  return request({
    url: '/snapshot/transfer/changeTrStatus/' + id + '/' + status,
    method: 'post'
  })
}

//截屏
export function screenshot(code) {
  return request({
    url:'/snapshot/transfer/screenshot?code=' + code,
    method: 'get'
  })
}
