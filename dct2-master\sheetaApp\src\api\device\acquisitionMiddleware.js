import request from '@/utils/request'

// 查询设备列表
export function amList(query) {
  return request({
    url: '/am/list',
    method: 'get',
    params: query
  })
}

// 导出设备
export function exportAm(query) {
  return request({
    url: '/am/export',
    method: 'get',
    params: query
  })
}

// 清理无效采集终端
export function delete4Invalid() {
  return request({
    url: '/am/delete4Invalid',
    method: 'delete'
  })
}

// 删除对应采集终端
export function delAcm(ids) {
  return request({
    url: '/am/' + ids,
    method: 'delete'
  })
}
