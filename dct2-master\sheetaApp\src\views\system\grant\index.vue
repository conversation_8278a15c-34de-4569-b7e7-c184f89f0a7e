<template>
  <div class="app-container" style="text-align: center">
      <!--<el-button-->
        <!--type="primary"-->
        <!--plain-->
        <!--icon="el-icon-upload2"-->
        <!--size="mini"-->
        <!--@click="handleImport(1)"-->
      <!--&gt;设备SN号授权-->
      <!--</el-button>-->

    <!-- 导入对话框 -->
    <!--<el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body @close='closeUpload'>-->
    <div style="padding-bottom: 20px">
      <span style="color: #1c84c6">设备SN号授权:</span>
    </div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".txt"
        :headers="upload.headers"
        :action="upload.url + '?type=' + upload.type"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" style="color:red;padding-bottom: 20px" slot="tip">提示：仅允许导入“txt”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    <!--</el-dialog>-->
  </div>
</template>

<script>
  import {
    importSnAuth
  } from '@/api/system/grant'
  import { getToken } from '@/utils/auth'
  export default {
    name: "Grant",
    data() {
      return {
        // 是否显示弹出层
        open: false,
        loading: true,
        // 弹出层标题
        title: "",
        // 导入参数
        upload: {
          // 是否显示弹出层（用户导入）
          open: false,
          // 弹出层标题（用户导入）
          title: "",
          // 是否禁用上传
          isUploading: false,
          // 配置文件类型，1：sn 2：License
          type: 1,
          // 设置上传的请求头部
          headers: {Authorization: "Bearer " + getToken()},
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/grant/auth/import"
        }
      };
    },
    created() {

    },
    methods: {
      /** 导入按钮操作 */
      handleImport(type) {
        this.upload.title = "授权导入";
        this.upload.open = true;
        this.upload.type = type;
      },
      closeUpload() {
      },
      // 文件上传中处理
      handleFileUploadProgress(event, file, fileList) {
        this.upload.isUploading = true;
      },
      // 文件上传成功处理
      handleFileSuccess(response, file, fileList) {
        this.upload.open = false;
        this.upload.isUploading = false;
        this.$refs.upload.clearFiles();
        this.$alert(response.msg, "导入结果", {dangerouslyUseHTMLString: true});
      },
      // 提交上传文件
      submitFileForm() {
        this.$refs.upload.submit();
      }
    }
  };
</script>
