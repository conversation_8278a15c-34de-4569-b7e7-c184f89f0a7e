<template>
  <div class="app-container" id="deviceDiv">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="通道名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入通道名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['snapshot:channel:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['snapshot:channel:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['snapshot:channel:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['snapshot:channel:export']"
        >导出
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="channelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="45" align="center"/>
      <el-table-column label="通道名称" align="center" prop="name" :show-overflow-tooltip="true"/>
      <el-table-column label="通道地址" align="center" prop="address" :show-overflow-tooltip="true"/>
      <el-table-column label="通道状态" align="center" prop="status" :show-overflow-tooltip="true"/>
      <el-table-column label="快照时间" align="center" prop="operateTime" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.operateTime|formatDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="快照预览" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="showChanelImg(scope.row)"
            v-hasPermi="['snapshot:channel:edit']"
          >预览
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="存储状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.trStatus"
            :active-value="1"
            :inactive-value="0"
            @change="changeStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="传输状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.transferStatus"
            :active-value="1"
            :inactive-value="0"
            @change="changeTrStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['snapshot:channel:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['snapshot:channel:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!--快照弹出框-->
    <el-dialog :title="snapshotTitle" :visible.sync="snapshotOpen" width="800px" @close="snapshotCancel">
      <div>
        <img v-if="snapshotUrl" alt="快照" :src="snapshotUrl"/>
      </div>
    </el-dialog>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" >
      <el-form ref="form" :model="form" :rules="rules" label-width="180px" v-loading="submitLoading">
        <el-form-item label="通道名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入通道名称"/>
        </el-form-item>

        <el-form-item label="通道地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入通道地址"  style="width: 380px" @input="addressChange"/>
          <el-button
            size="mini"
            type="text"
            :loading="preLoading"
            :style="{ display: preVisible }"
            @click="show(form.address)"
          >预览
          </el-button>
          <!-- v-hasPermi="['snapshot:channel:screenshot']" -->
        </el-form-item>
        <el-form-item label="分辨率" prop="resolvingPower">
          <el-select v-model="form.resolvingPower" placeholder="请选择分辨率">
            <el-option
              v-for="rp in resolvingPowerOptions"
              :key="rp"
              :label="rp"
              :value="rp"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="传输间隔" prop="compartment">
          <el-input v-model="form.compartment" placeholder="请输入传输间隔" v-show="false"/>
          <el-select v-model="compartmentNum">
            <el-option
              v-for="num in numOptions"
              :key="num"
              :label="num"
              :value="num"
            ></el-option>
          </el-select>
          <el-select v-model="compartmentUnit">
            <el-option
              v-for="unit in unitOptions"
              :key="unit.val"
              :label="unit.label"
              :value="unit.val"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="cost">
          <el-select v-model="form.cost" placeholder="请选择优先级">
            <el-option
              v-for="co in costOptions"
              :key="co"
              :label="co"
              :value="co"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="本地保存时间（单位天）" prop="storageTime">
          <el-input v-model="form.storageTime" placeholder="请输入天数"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 预览 -->
    <el-dialog :title="预览" :visible="preview" width="650px" @close='closeDialog'>
      <el-image :src="address"></el-image>
    </el-dialog>
  </div>
</template>

<script>
  import {addChannel, channelList, delChannel, exportChannel, getChannel, updateChannel,screenshot,changeStatus,changeTrStatus} from '@/api/snapshot/channel'

  export default {
    name: 'SnapshotChannel',
    data() {
      return {
        // 遮罩层
        loading: true,
        submitLoading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 参数表格数据
        channelList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: undefined
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          label: [
            {required: true, message: '通道名称不能为空', trigger: 'blur'}
          ],
          address: [
            {required: true, message: '通道地址不能为空', trigger: 'blur'}
          ],
          compartment: [
            {required: true, message: '传输间隔不能为空', trigger: 'blur'}
          ]
        },
        preview: false,
        address: "",
        preLoading: false,
        preVisible: 'none',
        snapshotTitle: '',
        snapshotOpen: false,
        snapshotUrl: '',
        resolvingPowerOptions: ['320*180', '384*216', '480*270', '640*360', '768*432', '960*540', '1440*810', '1920*1080'],
        // 优先级
        costOptions: [10, 20, 30, 40, 50],
        // 传输间隔
        numOptions: [1, 2, 3, 4, 5, 6, 10, 12, 15, 20, 30],
        // 传输间隔
        unitOptions: [{val: 1, label: '秒'}, {val: 2, label: '分'}],
        compartmentNum: 5,
        compartmentUnit: 2
      }
    },

    created() {
      this.getList();
    },
    filters: {
      formatDate: function (value) {
        if (value === null || value ===undefined) {
          return '';
        }
        let date = new Date(value);
        let y = date.getFullYear();
        let MM = date.getMonth() + 1;
        MM = MM < 10 ? ('0' + MM) : MM;
        let d = date.getDate();
        d = d < 10 ? ('0' + d) : d;
        let h = date.getHours();
        h = h < 10 ? ('0' + h) : h;
        let m = date.getMinutes();
        m = m < 10 ? ('0' + m) : m;
        let s = date.getSeconds();
        s = s < 10 ? ('0' + s) : s;
        return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
      }
    },
    methods: {
      addressChange(){
        this.form.address = this.form.address.trim();
        if(this.form.address !== null && this.form.address !== '' && this.form.address !== undefined){
          this.preVisible = '';
        }else {
          this.preVisible = 'none';
        }
      },

      show(address){
        this.preLoading = true;
        let form = {'address': address};
        screenshot(form).then(response => {
          if (response.code === 200) {
            this.address = response.msg;
          } else {
            this.msgError(response.msg);
          }
        });
        setTimeout(() =>{
          this.preLoading = false;
          this.preview = true;
        },2000);
      },

      closeDialog(){
        this.preview = false;
      },

      /** 查询通道列表 */
      getList() {
        this.loading = true;
        channelList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
            this.channelList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        )
      },

      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },

      // 表单重置
      reset() {
        this.form = {
          id: undefined,
          code: undefined,
          name: undefined,
          address: undefined,
          storageTime: 90,
          cost: 40
        };
        this.resetForm('form');
        this.submitLoading = false;
      },

      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },

      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },

      /** 新增按钮操作 */
      handleAdd() {
        var _this = this;
        _this.reset();
        _this.open = true;
        _this.title = '添加通道';
        if(_this.form.address !== null && _this.form.address !== '' && _this.form.address !== undefined){
          _this.preVisible = '';
        }else{
          _this.preVisible = 'none';
        }
      },

      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },

      /** 修改按钮操作 */
      handleUpdate(row) {
        var _this = this;
        this.reset();
        const id = row.id || _this.ids;
        getChannel(id).then(response => {
          _this.form = response.data;
          _this.open = true;
          _this.title = '修改参数';
          if(response.data.address !== null && response.data.address !== '' && response.data.address !== undefined){
            _this.preVisible = '';
          }else{
            _this.preVisible = 'none';
          }
          if (_this.form.compartment >= 60) {
            _this.compartmentNum = _this.form.compartment / 60;
            _this.compartmentUnit = 2;
          } else {
            _this.compartmentNum = _this.form.compartment;
            _this.compartmentUnit = 1;
          }
        });
      },

      /** 提交按钮 */
      submitForm: function () {
        var _this = this;
        _this.compartmentChange();
        this.submitLoading = true;
        _this.$refs['form'].validate(valid => {
          if (valid) {
            if (_this.form.id !== undefined) {
              updateChannel(this.form).then(response => {
                if (response.code === 200) {
                  _this.msgSuccess('修改成功');
                  _this.open = false;
                  _this.getList();
                } else {
                  this.submitLoading = false;
                  _this.msgError(response.msg);
                }
              })
            } else {
              addChannel(this.form).then(response => {
                if (response.code === 200) {
                  _this.msgSuccess('新增成功');
                  _this.open = false;
                  _this.getList();
                } else {
                  this.submitLoading = false;
                  _this.msgError(response.msg);
                }
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        var _this = this;
        const ids = row.id || this.ids;
        this.$confirm('是否确认删除选择的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
           delChannel(ids).then(res=> {
             if (res.code === 200) {
               _this.getList();
               _this.msgSuccess('删除成功');
             }
           })
        }).catch(function () {
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        var _this = this;
        const queryParams = _this.queryParams;
        this.$confirm('是否确认导出所有数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return exportChannel(queryParams);
        }).then(response => {
          _this.download(response.msg)
        }).catch(function () {
        })
      },
      showChanelImg(row) {
        this.snapshotTitle = row.fileName;
        this.snapshotOpen = true;
        this.snapshotUrl = row.directory + '/' + row.fileName;
      },
      /** 窗口关闭 */
      snapshotCancel() {
        this.snapshotOpen = false;
      },
      /** 存储状态修改*/
      changeStatus(row) {
        let that = this;
        let text = row.trStatus === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.name + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return changeStatus(row.code, row.trStatus);
        }).then(() => {
          this.msgSuccess(text + '成功');
        }).catch(function () {
          row.trStatus = row.trStatus === 0 ? 1 : 0;
        });
      },
      compartmentChange() {
        this.form.compartment = this.compartmentNum;
        if (this.compartmentUnit === 2) {
          this.form.compartment = this.compartmentNum * 60;
        }
      },
      /** 传输状态修改*/
      changeTrStatus(row) {
        let that = this;
        let text = row.transferStatus === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.name + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return changeTrStatus(row.code, row.transferStatus);
        }).then(() => {
          this.msgSuccess(text + '成功');
        }).catch(function () {
          row.transferStatus = row.transferStatus === 0 ? 1 : 0;
        });
      }
    }
  }
</script>

