<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['device:info:export']"
        >导出
        </el-button>
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
          v-hasPermi="['device:info:remove']"
        >清除
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="amList" @selection-change="handleSelectionChange">
      <el-table-column label="名称" align="center" prop="name" :show-overflow-tooltip="true"/>
      <!--<el-table-column label="解析模版" align="center" prop="type" :formatter="deviceTypeFormat"-->
                       <!--:show-overflow-tooltip="true"/>-->
      <el-table-column label="模块型号" align="center" prop="moduleModel" :show-overflow-tooltip="true"/>
      <el-table-column label="MAC地址" align="center" prop="mac" :show-overflow-tooltip="true"/>
      <el-table-column label="IP" align="center" prop="ip" :show-overflow-tooltip="true"/>
      <el-table-column label="端口" align="center" prop="port" :show-overflow-tooltip="true"/>
      <el-table-column label="连接状态" align="center" prop="connectStatus" :formatter="connectStatusFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="数据预览" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="openRealTimeData(scope.row)"
            v-hasPermi="['device:info:edit']"
          >查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="终端绑定" align="center" prop="deviceStatus" :formatter="deviceStatusFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleSelectDelete(scope.row)"
            v-hasPermi="['device:info:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
      <!--<el-table-column label="创建时间" align="center" prop="createTime" width="180">-->
        <!--<template slot-scope="scope">-->
          <!--<span>{{ parseTime(scope.row.createTime) }}</span>-->
        <!--</template>-->
      <!--</el-table-column>-->
      <!--<el-table-column label="修改时间" align="center" prop="updateTime" width="180">-->
        <!--<template slot-scope="scope">-->
          <!--<span>{{ parseTime(scope.row.updateTime) }}</span>-->
        <!--</template>-->
      <!--</el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!--实时数据弹出框-->
    <el-dialog :title="rtTitle" :visible.sync="rtOpen" width="800px" @close="rtCancel">
      <div>
        <div>
          <el-button type="primary" @click="initWebSocket" v-show="!wsConnect">连接</el-button>
          <el-button type="danger" @click="closeWebsocket" v-show="wsConnect">暂停</el-button>
          <el-button type="primary" @click="cleanShowDiv">清空</el-button>
        </div>

        <div class="show" id="showDiv"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {amList, delete4Invalid, exportAm,delAcm} from '@/api/device/acquisitionMiddleware'
  import $ from 'jquery'

  export default {
    name: 'AcquisitionMiddleware',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 参数表格数据
        amList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 类型数据字典
        typeOptions: [],
        // 日期范围
        dateRange: [],
        // 设备类型
        deviceTypeOptions: [],
        // 优先级
        costOptions: [10, 20, 30, 40, 50],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: undefined
        },
        rtTitle: '',
        rtOpen: false,
        rtMac: '',
        socket: null,
        sid: null,
        wsConnect: false,
        wsMessageLength: 0,
        type:null
      }
    },
    created() {
      this.getDicts("device_type").then(response => {
        this.deviceTypeOptions = response.data;
      }).then(this.getList());
    },
    methods: {
      /** 查询设备列表 */
      getList() {
        this.loading = true;
        amList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
            this.amList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        )
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },
      deviceTypeFormat(row, column) {
        if (row.type === undefined || row.type === '' || row.type === null) {
          return '';
        }
        return this.selectDictLabel(this.deviceTypeOptions, row.type.toString());
      },
      connectStatusFormat(row, column) {
        if (row.connectStatus === 1) {
          return "连接";
        }
        return "中断";
      },
      deviceStatusFormat(row, column) {
        if (row.deviceStatus === 1) {
          return "已绑定";
        }
        return "未绑定";
      },
      handleDelete() {
        let that = this;
        this.$confirm('是否确认清除无效采集终端?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          that.loading = true;
          return delete4Invalid();
        }).then(() => {
          that.getList();
          that.msgSuccess('删除成功');
          that.loading = false;
        }).catch(function () {
          that.loading = false;
        })
      },
      /**列表 删除按钮操作 */
      handleSelectDelete(row) {
        const ids = row.id || this.ids;
        let that = this;
        this.$confirm('是否确认删除选择的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          that.loading = true;
          return delAcm(ids);
        }).then(() => {
          that.getList();
          that.msgSuccess('删除成功');
          that.loading = false;
        }).catch(function () {
          that.loading = false;
        })
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
      },
      /** 导出按钮操作 */
      handleExport() {
        const queryParams = this.queryParams;
        this.$confirm('是否确认导出所有数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return exportAm(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function () {
        })
      },
      /** 数据窗口弹出 */
      openRealTimeData(row) {
        this.rtOpen = true;
        this.rtTitle = '实时数据';
        this.rtMac = row.mac;
        this.type = row.type;
        this.initWebSocket();
      },
      /** 数据窗口关闭 */
      rtCancel() {
        this.closeWebsocket();
        this.cleanShowDiv();
        this.rtOpen = false;
      },
      //初始化websocket
      initWebSocket() {
        this.sid = 'sid-' + (new Date()).getTime();
        const wsUri = 'ws://' + window.location.host + ':9991/wsServer/' + this.sid + '/' + this.rtMac+ '/' + 0+ '/' + 0;
        this.socket = new WebSocket(wsUri);
        this.socket.onmessage = this.websocketOnmessage;
        this.socket.onopen = this.websocketOnopen;
        this.socket.onerror = this.websocketOnerror;
        this.socket.onclose = this.websocketClose;

        this.monitorFun(1);
      },
      websocketOnopen() { //连接建立之后执行send方法发送数据
        console.log('websocket连接成功');
        this.wsConnect = true;
      },
      websocketOnerror() {//连接建立失败重连
        console.log('websocket连接失败');
        this.wsConnect = false;
      },
      websocketOnmessage(e) { //数据接收
        this.wsMessageLength++;

        if (this.wsMessageLength === 1) {
          $('.show').append('<p id="sm_' + this.wsMessageLength + '">' + e.data + '</p>');
        } else {
          $('#sm_' + (this.wsMessageLength - 1)).after('<p id="sm_' + this.wsMessageLength + '">' + e.data + '</p>');
        }

        if (this.wsMessageLength > 1000) {
          $('#sm_' + (this.wsMessageLength - 1000)).remove();
        }

        let showDiv = document.getElementById('showDiv');
        showDiv.scrollTop = showDiv.scrollHeight;
      },
      websocketClose() {
        console.log('websocket断开连接');
        this.wsConnect = false;
      },
      closeWebsocket() {
        this.socket.close();
      },
      cleanShowDiv() {
        $('.show').empty();
        this.wsMessageLength = 0;
      },
      monitorFun(t) {
        let time = t || 2;
        let userTime = time * 60;
        let that = this;
        let objTime = {
          init: 0,
          time: function () {
            objTime.init += 1;
            if (!that.wsConnect) {
              objTime.destroyFun();
            }

            if (objTime.init === userTime) {
              that.closeWebsocket();
              objTime.destroyFun();
            }
          },
          eventFun: function () {
            objTime.clearFun();
            monitorTest = setInterval(objTime.time, 1000);
          },
          clearFun: function () {
            clearInterval(monitorTest);
            objTime.init = 0;
          },
          destroyFun: function () {
            objTime.clearFun();
            $('#deviceDiv').unbind('click').unbind('keydown').unbind('mousemove').unbind('mousewheel');
          }
        };
        let monitorTest = setInterval(objTime.time, 1000);

        $('#deviceDiv').bind('click', objTime.eventFun).bind('keydown', objTime.eventFun)
          .bind('mousemove', objTime.eventFun).bind('mousewheel', objTime.eventFun);
      }
    }
  }
</script>
