import request from '@/utils/request'
// 查询参数列表
export function listSerials(query) {
  return request({
    url: '/serialConfig/list',
    method: 'get',
    params: query
  })
}

// 查询原始串口列表
export function getOldFreeSerialPort() {
  return request({
    url: '/device/getOldFreeSerialPort',
    method: 'get'
  })
}
//新增串口配置
export function addSerial(data) {
  return request({
    url: '/serialConfig',
    method: 'post',
    data: data
  })
}

// 修改串口配置
export function updateSerial(data) {
  return request({
    url: '/serialConfig',
    method: 'put',
    data: data
  })
}

// 删除串口配置
export function delSerial(ids) {
  return request({
    url: '/serialConfig/' + ids,
    method: 'delete'
  })
}

// 查询串口配置详细
export function getSerial(id) {
  return request({
    url: '/serialConfig/' + id,
    method: 'get'
  })
}
