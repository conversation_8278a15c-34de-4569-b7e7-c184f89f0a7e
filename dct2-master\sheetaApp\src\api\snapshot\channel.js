import request from '@/utils/request'

// 查询通道配置列表
export function channelList(query) {
  return request({
    url: '/snapshot/channel/list',
    method: 'get',
    params: query
  })
}

// 导出通道
export function exportChannel(query) {
  return request({
    url: '/snapshot/channel/export',
    method: 'get',
    params: query
  })
}

//查询通道配置详细
export function getChannel(id) {
  return request({
    url: '/snapshot/channel/'+ id,
    method: 'get'
  })
}

//修改通道配置
export function updateChannel(data) {
  return request({
    url:'/snapshot/channel',
    method:'put',
    data:data
  })
}

//新增通道配置
export function addChannel(data) {
  return request({
    url:'/snapshot/channel',
    method:'post',
    data:data
  })
}
//删除通道配置
export function delChannel(id) {
  return request({
    url:'/snapshot/channel/'+ id,
    method: 'delete'
  })
}
//截屏
export function screenshot(address) {
  return request({
    url:'/snapshot/channel/screenshot',
    method: 'post',
    data:address
  })
}

// 存储状态修改
export function changeStatus(code, status) {
  return request({
    url: '/snapshot/channel/changeStatus/' + code + '/' + status,
    method: 'post'
  })
}
  // 传输状态修改
  export function changeTrStatus(code, status) {
    return request({
      url: '/snapshot/channel/changeTrStatus/' + code + '/' + status,
      method: 'post'
    })
}
