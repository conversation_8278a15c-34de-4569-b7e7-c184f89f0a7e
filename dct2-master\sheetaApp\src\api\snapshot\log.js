import request from '@/utils/request'

// 查询通道配置列表
export function logList(query) {
  return request({
    url: '/snapshot/log/list',
    method: 'get',
    params: query
  })
}

// 导出通道
export function exportLog(query) {
  return request({
    url: '/snapshot/log/export',
    method: 'get',
    params: query
  })
}

// 查询快照通道
export function allChannelList() {
  return request({
    url: '/snapshot/channel/allList',
    method: 'get'
  })
}

