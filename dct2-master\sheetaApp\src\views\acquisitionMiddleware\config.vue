<template>
  <div class="app-container">
        <el-form ref="form" :model="form" :rules="rules" label-width="150px" >
      <el-form-item label="串口名称" prop="serialPort" >
        <el-select v-model="form.serialPort" placeholder="请选择串口" >
      <el-option
        v-for="sp in serialPortOptions"
        :key="sp"
        :label="sp"
        :value="sp"
      ></el-option>
    </el-select>
      </el-form-item>
          <el-form-item label="模块型号" prop="moduleModel" >
            <el-select v-model="form.moduleModel" placeholder="请选择模块型号" >
              <el-option
                v-for="sp in moduleModelPortOptions"
                :key="sp"
                :label="sp"
                :value="sp"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="采集节点名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入采集节点名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="IP地址" prop="ip">
            <el-input
              v-model="form.ip"
              placeholder="请输入IP地址"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="子网掩码" prop="mask">
            <el-input
              v-model="form.mask"
              placeholder="请输入子网掩码"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="网关" prop="gateway">
            <el-input
              v-model="form.gateway"
              placeholder="请输入网关"
              style="width: 200px"
            />
          </el-form-item>
           <el-form-item label="采集节点数据传输" prop="states">
          <el-switch
            v-model="form.states"
            :active-value="1"
            :inactive-value="0"
            @change="enableChange(form.states)"
          ></el-switch>
          </el-form-item>
          <el-form-item label="波特率" prop="baudRate" v-show="baudRateVisible">
            <el-select v-model="form.baudRate" filterable allow-create placeholder="请选择波特率" >
              <el-option
                v-for="dt in baudRateOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="数据位" prop="dataBits" v-show="dataBitsVisible">
            <el-select v-model="form.dataBits" placeholder="请输入数据位" >
              <el-option
                v-for="dt in dataBitsOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
            <!--<el-input v-model="form.dataBits" placeholder="请输入数据位"/>-->
          </el-form-item>

          <el-form-item label="停止位" prop="stopBits" v-show="stopBitsVisible">
            <el-select v-model="form.stopBits" placeholder="请输入停止位" >
              <el-option
                v-for="dt in stopBitsOptions"
                :key="dt.dictLabel"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
            <!--<el-input v-model="form.stopBits" placeholder="请输入停止位"/>-->
          </el-form-item>

          <el-form-item label="校验位" prop="parity" v-show="parityVisible">
            <el-select v-model="form.parity" placeholder="请输入校验位" >
              <el-option
                v-for="dt in parityOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
            <!--<el-input v-model="form.parity" placeholder="请输入校验位"/>-->
          </el-form-item>
          <el-form-item label="船舶终端服务IP" prop="shipIp" v-show="shipIpVisible">
            <el-input
              v-model="form.shipIp"
              placeholder="请输入船舶终端服务IP"
              style="width: 200px"
            />
          </el-form-item>
         </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm" :disabled="submitLoading">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import { getFreeSerialPort,writeSerialPort } from "@/api/device/config";
import $ from 'jquery';
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "config",
  data() {
    return {
      // 表单参数
      form: {},
      submitLoading: false,
      // 表单校验
      rules: {
        ip: [
          {required: true, message: 'ip不能为空', trigger: 'blur'}
        ],
        serialPort: [
          {required: true, message: '串口不能为空', trigger: 'blur'}
        ],
        name: [
          {required: true, message: '采集节点不能为空', trigger: 'blur'}
        ]
      },
      baudRateVisible:false,
      dataBitsVisible:false,
      stopBitsVisible:false,
      parityVisible:false,
      shipIpVisible:false,
      //波特率
      baudRateOptions:[],
      //数据位
      dataBitsOptions:[],
      //停止位
      stopBitsOptions:[],
      //校验位
      parityOptions:[],
// 串口名称
      serialPortOptions: [],
      // 模块型号
      moduleModelPortOptions: ['型号1','型号2'],
    };
  },
  created() {
    getFreeSerialPort().then(response => {
      this.serialPortOptions = response.data;
      this.loading = false;
    });
    this.getDicts("baud_rate").then(response => {
      for (let i = 0; i < response.data.length; i++) {
        let typeMap = {dictValue: 0, dictLabel: ''};
        typeMap.dictValue = parseInt(response.data[i].dictValue);
        typeMap.dictLabel = response.data[i].dictLabel;
        this.baudRateOptions[i] = typeMap;
      }
    });
    this.getDicts("date_bits").then(response => {
      for (let i = 0; i < response.data.length; i++) {
        let typeMap = {dictValue: 0, dictLabel: ''};
        typeMap.dictValue = parseInt(response.data[i].dictValue);
        typeMap.dictLabel = response.data[i].dictLabel;
        this.dataBitsOptions[i] = typeMap;
      }
    });
    this.getDicts("stop_bits").then(response => {
      for (let i = 0; i < response.data.length; i++) {
        let typeMap = {dictValue: 0, dictLabel: ''};
        typeMap.dictValue = parseInt(response.data[i].dictValue);
        typeMap.dictLabel = response.data[i].dictLabel;
        this.stopBitsOptions[i] = typeMap;
      }
    });
    this.getDicts("parity").then(response => {
      for (let i = 0; i < response.data.length; i++) {
        let typeMap = {dictValue: 0, dictLabel: ''};
        typeMap.dictValue = parseInt(response.data[i].dictValue);
        typeMap.dictLabel = response.data[i].dictLabel;
        this.parityOptions[i] = typeMap;
      }
    });
  },
  methods: {
    enableChange(row){
      //开关显示、隐藏串口配置信息
      if (row === 0){
        this.baudRateVisible=false;
        this.dataBitsVisible=false;
        this.stopBitsVisible=false;
        this.parityVisible=false;
        this.shipIpVisible=false;
      } else{
        this.baudRateVisible=true;
        this.dataBitsVisible=true;
        this.stopBitsVisible=true;
        this.parityVisible=true;
        this.shipIpVisible=true;
      }
      return row === 0 ? 1 : 0;
    },
    /** 提交按钮 */
    submitForm: function () {
      this.submitLoading = true;
      this.$refs['form'].validate(valid => {
        if (valid) {
          writeSerialPort(this.form).then(response => {
            if (response.code === 200) {
              this.msgSuccess('新增成功');
              this.form.states=1;
              this.submitLoading = false;
              // this.open = false;
              // this.getList();
            } else {
              this.submitLoading = false;
              this.msgError(response.msg);
            }
          });
        }
      })
    },
    // 取消按钮
    cancel() {
      this.reset();
    },
    // 表单重置
    reset() {

    }
  }
};
</script>
