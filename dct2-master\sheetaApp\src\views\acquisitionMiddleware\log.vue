<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['device:info:export']"
        >导出
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="logList">
      <el-table-column label="名称" align="center" prop="name" :show-overflow-tooltip="true"/>
      <el-table-column label="解析模版" align="center" prop="type" :formatter="deviceTypeFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="MAC地址" align="center" prop="mac" :show-overflow-tooltip="true"/>
      <el-table-column label="IP" align="center" prop="ip" :show-overflow-tooltip="true"/>
      <el-table-column label="端口" align="center" prop="port" :show-overflow-tooltip="true"/>
      <el-table-column label="动作" align="center" prop="action" :formatter="actionFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import {exportLog, logList} from '@/api/device/connectLog'

  export default {
    name: 'ConnectLog',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 参数表格数据
        logList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 类型数据字典
        typeOptions: [],
        // 日期范围
        dateRange: [],
        // 设备类型
        deviceTypeOptions: [],
        // 优先级
        costOptions: [10, 20, 30, 40, 50],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: undefined
        },
      }
    },
    created() {
      this.getDicts("device_type").then(response => {
        this.deviceTypeOptions = response.data;
      }).then(this.getList());
    },
    methods: {
      /** 查询设备列表 */
      getList() {
        this.loading = true;
        logList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
            this.logList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        )
      },
      deviceTypeFormat(row, column) {
        if (row.type === undefined || row.type === '' || row.type === null) {
          return '';
        }
        return this.selectDictLabel(this.deviceTypeOptions, row.type.toString());
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },
      /** 导出按钮操作 */
      handleExport() {
        const queryParams = this.queryParams;
        this.$confirm('是否确认导出所有数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return exportLog(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function () {
        })
      },
      actionFormat(row, column) {
        if (row.action === undefined || row.action === '' || row.action === null) {
          return '';
        }
        if(row.action === 1){
          return '连接';
        } else if(row.action === 2){
          return '断开';
        } else if(row.action === 3){
          return '更新';
        }
        return '';
      }
    }
  }
</script>
