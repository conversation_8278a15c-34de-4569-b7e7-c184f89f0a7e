<template>
  <div class="app-container" id="deviceDiv">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="快照通道">
        <el-select v-model="queryParams.channelCode" placeholder="请选择快照通道" clearable @keyup.enter.native="handleQuery">
          <el-option
            v-for="channel in channelOptions"
            :key="channel.code"
            :label="channel.name"
            :value="channel.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['snapshot:log:export']">导出
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="logList">
      <el-table-column label="通道名称" align="center" prop="channelName" :show-overflow-tooltip="true"/>

      <el-table-column label="文件名称" align="center" prop="fileName" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <a @click.prevent="showImg(scope.row)" style="text-decoration-line: underline">{{scope.row.fileName}}</a>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!--快照弹出框-->
    <el-dialog :title="snapshotTitle" :visible.sync="snapshotOpen" width="800px" @close="snapshotCancel">
      <div>
        <img v-if="snapshotUrl" alt="快照" :src="snapshotUrl"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {allChannelList, exportLog, logList} from '@/api/snapshot/log'

  export default {
    name: 'SnapshotLog',
    data() {
      return {
        // 遮罩层
        loading: true,
        // 总条数
        total: 0,
        // 参数表格数据
        logList: [],
        // 日期范围
        dateRange: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          channelCode: undefined
        },
        // 快照通道
        channelOptions: [],
        // 表单参数
        form: {},
        snapshotTitle: '',
        snapshotOpen: false,
        snapshotUrl: ''
      }
    },
    created() {
      this.allChannelList();
      this.getList();
    },
    filters: {
      time: (value) => {
        if (value == null || value === "") return "";
        let date = new Date(value);
        let year = date.getFullYear();
        let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
        let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        let hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
        let minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
        let seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
      }
    },
    methods: {
      /** 查询通道列表 */
      getList() {
        var _this = this;
        _this.loading = true;
        logList(_this.addDateRange(_this.queryParams, _this.dateRange)).then(response => {
            _this.logList = response.rows;
            _this.total = response.total;
            _this.loading = false;
          }
        )
      },
      allChannelList() {
        var _this = this;
        _this.loading = true;
        allChannelList().then(response => {
            _this.channelOptions = response.data;
            _this.loading = false;
          }
        )
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },
      /** 导出按钮操作 */
      handleExport() {
        var _this = this;
        const queryParams = _this.queryParams;
        _this.$confirm('是否确认导出所有数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return exportLog(queryParams);
        }).then(response => {
          _this.download(response.msg)
        }).catch(function () {
        })
      },
      showImg(row) {
        this.snapshotTitle = row.fileName;
        this.snapshotOpen = true;
        this.snapshotUrl = row.directory + '/' + row.fileName;
      },
      /** 窗口关闭 */
      snapshotCancel() {
        this.snapshotOpen = false;
      }
    }
  }
</script>

