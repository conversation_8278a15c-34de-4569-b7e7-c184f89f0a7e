<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="原始串口名称" prop="oldName" >
        <el-select v-model="queryParams.oldName" placeholder="请选择原始串口">
          <el-option
            v-for="sp in serialPortOptions"
            :key="sp"
            :label="sp"
            :value="sp"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="系统使用名称" prop="newName">
        <el-input
          v-model="queryParams.newName"
          placeholder="请输入系统使用名称"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="serialList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="原始串口名称" align="center" prop="oldName" />
      <el-table-column label="系统使用名称" align="center" prop="newName" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="原始串口名称" prop="oldName" >
          <el-select v-model="form.oldName" placeholder="请选择原始串口">
            <el-option
              v-for="sp in serialPortOptions"
              :key="sp"
              :label="sp"
              :value="sp"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="系统使用名称" prop="newName">
          <el-input
            v-model="form.newName"
            placeholder="请输入系统使用名称"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { listSerials, delSerial, addSerial, updateSerial, getOldFreeSerialPort,getSerial } from "@/api/system/serialConfig";

  export default {
    name: "Serial",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 表格数据
        serialList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 状态数据字典
        statusOptions: [],
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          newName: undefined,
          oldName: undefined,
          remark: undefined
        },
        // 表单参数
        form: {},
        // 串口名称
        serialPortOptions: [],
        // 表单校验
        rules: {
          oldName: [
            { required: true, message: "原始串口名称不能为空", trigger: "blur" }
          ],
          newName: [
            { required: true, message: "系统使用名称不能为空", trigger: "blur" }
          ]
        }
      };
    },
    created() {
      this.getList();
      this.getOldFreeSerialPort();
    },
    methods: {
      /** 查询串口列表 */
      getList() {
        this.loading = true;
        listSerials(this.queryParams).then(response => {
          this.serialList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      },
      getOldFreeSerialPort() {
        this.loading = true;
        getOldFreeSerialPort().then(response => {
            this.serialPortOptions = response.data;
            this.loading = false;
          }
        )
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          newName: undefined,
          oldName: undefined,
          remark: undefined
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id)
        this.single = selection.length!=1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加串口配置";
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        getSerial(row.id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = '修改串口配置';
        });
      },
      /** 提交按钮 */
      submitForm: function() {
        let _this = this;
        this.$refs["form"].validate(valid => {
          if (valid) {
            if (this.form.id != undefined) {
              updateSerial(_this.form).then(response => {
                if (response.code === 200) {
                  _this.msgSuccess("修改成功");
                  _this.open = false;
                  _this.getList();
                } else {
                  _this.msgError(response.msg);
                }
              });
            } else {
              addSerial(_this.form).then(response => {
                if (response.code === 200) {
                  _this.msgSuccess("新增成功");
                  _this.open = false;
                  _this.getList();
                } else {
                  _this.msgError(response.msg);
                }
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        let _this = this;
        const serIds = row.id || this.ids;
        this.$confirm('是否确认删除串口编号为"' + row.oldName + '"的配置项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delSerial(serIds);
        }).then(() => {
          _this.getList();
          _this.msgSuccess("删除成功");
        }).catch(function() {});
      }
    }
  };
</script>
