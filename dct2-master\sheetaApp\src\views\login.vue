<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">设备后台管理系统</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <Verify
        @success="capctchaCheckSuccess"
        :mode="'pop'"
        :captchaType="'blockPuzzle'"
        :imgSize="{ width: '330px', height: '155px' }"
        ref="verify"
      ></Verify>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <!--<span>Copyright © 2018-2019 xxx All Rights Reserved.</span>-->
    </div>
  </div>
</template>

<script>
  import Cookies from "js-cookie";
  import { encrypt, decrypt } from '@/utils/jsencrypt'
  import Verify from "@/components/Verifition/Verify";

  export default {
    components: { Verify },
    name: "Login",
    data() {
      return {
        codeUrl: "",
        cookiePassword: "",
        loginForm: {
          username: "",//admin
          password: "",//admin123
          rememberMe: false,
          code: "",
          uuid: "",
          num: 5,
          isBlock: [],
        },
        loginRules: {
          username: [
            { required: true, trigger: "blur", message: "用户名不能为空" }
          ],
          password: [
            { required: true, trigger: "blur", message: "密码不能为空" }
          ]
          // ,code: [{ required: true, trigger: "change", message: "验证码不能为空" }]
        },
        loading: false,
        redirect: undefined,
        total: 60,
      };
    },
    watch: {
      $route: {
        handler: function(route) {
          this.redirect = route.query && route.query.redirect;
        },
        immediate: true
      }
    },
    created() {
      this.getCookie();
      this.loginForm.num = 5;
      this.total = 60;
      this.loginForm.isBlock =[];
    },
    methods: {
      getCookie() {
        const username = Cookies.get("username");
        const password = Cookies.get("password");
        const rememberMe = Cookies.get('rememberMe');
        this.loginForm = {
          username: username === undefined ? this.loginForm.username : username,
          password: password === undefined ? this.loginForm.password : decrypt(password),
          rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
        };
      },
      capctchaCheckSuccess(params) {
        let _this = this;
        _this.loginForm.code = params.captchaVerification;
        _this.loading = true;
        if (_this.loginForm.rememberMe) {
          Cookies.set("username", _this.loginForm.username, { expires: 30 });
          Cookies.set("password", encrypt(_this.loginForm.password), { expires: 30, });
          Cookies.set("rememberMe", _this.loginForm.rememberMe, { expires: 30 });
        } else {
          Cookies.remove("username");
          Cookies.remove("password");
          Cookies.remove("rememberMe");
        }
        _this.$store.dispatch("Login", this.loginForm).then((res) => {
          _this.loginForm.num = res;

          if(_this.loginForm.num >= 1) {
            _this.loginForm.num = _this.loginForm.num - 1;
          }

          if(_this.loginForm.num >= 1){
            _this.$message({
              message: '账号或者密码错误，你还有' + _this.loginForm.num + '次机会',
              type: 'warning'
            });
          }else if(_this.loginForm.num < 1){
            var message = {};
            message.user = _this.loginForm.username;
            message.flag = true;
            _this.loginForm.isBlock.push(message);
            let intervalFunc = window.setInterval(() => {
              if(_this.total > 0){
                _this.total = _this.total -1;
              }else{
                window.clearInterval(intervalFunc)
                _this.loginForm.num = 5;

                _this.total = 60;
                for(let i = 0;i< _this.loginForm.isBlock.length;i++){
                  if(_this.loginForm.isBlock[i].user===_this.loginForm.username){
                    delete _this.loginForm.isBlock[i];
                  }
                }
              }
            },1000);
            if(_this.total > 0){
              _this.$message({
                message: '错误登录超过5次，请'+_this.total+'秒后重新尝试登录',
                type: 'warning'
              });
            }
          }
          _this.$router.push({ path: this.redirect || "/" }).catch(() => {});
        }).catch(() => {
          _this.loading = false;
        });
      },
      handleLogin() {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            this.$refs.verify.show();
          }
        });
      }
      // handleLogin() {
      //   this.$refs.loginForm.validate(valid => {
      //     if (valid) {
      //       this.loading = true;
      //       if (this.loginForm.rememberMe) {
      //         Cookies.set("username", this.loginForm.username, { expires: 30 });
      //         Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
      //         Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
      //       } else {
      //         Cookies.remove("username");
      //         Cookies.remove("password");
      //         Cookies.remove('rememberMe');
      //       }
      //       this.$store
      //         .dispatch("Login", this.loginForm)
      //         .then(() => {
      //           this.$router.push({ path: this.redirect || "/" });
      //         })
      //         .catch(() => {
      //           this.loading = false;
      //           // this.getCode();
      //         });
      //     }
      //   });
      // }
    }
  };
</script>

<style rel="stylesheet/scss" lang="scss">
  .login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: darkolivegreen;
  }
  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }

  .login-form {
    border-radius: 6px;
    background: #ffffff;
    width: 400px;
    padding: 25px 25px 5px 25px;
    .el-input {
      height: 38px;
      input {
        height: 38px;
      }
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }
  }
  .login-tip {
    font-size: 13px;
    text-align: center;
    color: #bfbfbf;
  }
  .login-code {
    width: 33%;
    height: 38px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }
  .el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
</style>
