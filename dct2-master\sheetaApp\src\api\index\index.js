import request from '@/utils/request'

// 获取服务器时间
export  function getDateTime(){
  return request({
    url:'/index/date',
    method:'get'
  })
}
// 获取服务器运行时长，返回的是秒数
export  function  getSecondsTime(){
  return request({
    url:'/index/runTime',
    method:'get'
  })
}

// 设备接入量获取
export  function getAccessCount(){
  return request({
    url:'/index/accessCount',
    method:'get'
  })
}

// 获取设备参数量
export  function  getDeviceParamsCount(){
  return request({
    url:'/index/deviceParamsCount',
    method:'get'
  })
}
// 获取快照通道数
export  function  getSnapShotChannelCount(){
  return request({
    url:'/index/snapshotChannelCount',
    method:'get'
  })
}
// 获取系统运行状态
export  function  getSysRunStatus(){
  return request({
    url:'/index/sysRunStatus',
    method:'get'
  })
}

export function getAcquisitionTransmission(key){
  return request({
    url:'/index/acquisitionTransmission/?type='+key,
    method:'get',
  })
}

// 根据参数请求获取不同时间的设备数据
export  function getdataDeviceList(key){
  return request({
    url:'/index/dataDeviceList/?type='+key,
    method:'get'
  })
}
// 根据参数请求不同时间的快照数据
export  function  getdataSnapshotList(key){
  return request({
    url:'/index/dataSnapshotList/?type='+key,
    method:'get'
  })
}

export  function  getDeskUsage(){
  return request({
    url:'/index/getDeskUsage',
    method:'get'
  })
}



