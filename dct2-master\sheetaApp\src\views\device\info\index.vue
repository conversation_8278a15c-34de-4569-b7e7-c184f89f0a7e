<template>
  <div class="app-container" id="deviceDiv">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入设备名称"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入IP"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['device:info:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['device:info:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['device:info:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['device:info:export']"
        >导出
        </el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="45" align="center"/>
      <el-table-column label="设备名称" align="center" prop="name" :show-overflow-tooltip="true"/>
      <el-table-column label="连接方式" align="center" prop="connectType" :formatter="connectTypeFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="接口信息" align="center" :show-overflow-tooltip="true" :formatter="interfaceFormat"/>
      <!--<el-table-column label="IP" align="center" prop="ip" :show-overflow-tooltip="true"/>-->
      <!--<el-table-column label="端口" align="center" prop="port" :show-overflow-tooltip="true"/>-->
      <!--<el-table-column label="解析模版" align="center" prop="type" :formatter="deviceTypeFormat"-->
      <!--:show-overflow-tooltip="true"/>-->
      <!--<el-table-column label="设备编码" align="center" prop="code" :show-overflow-tooltip="true"/>-->
      <!--<el-table-column label="优先级" align="center" prop="cost" :show-overflow-tooltip="true"/>-->
      <el-table-column label="连接状态" align="center" prop="connectStatus" :formatter="connectStatusFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="数据模版" align="center" prop="type" :formatter="deviceTypeFormat"
                       :show-overflow-tooltip="true"/>
      <el-table-column label="数据预览" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="openRealTimeData(scope.row)"
            v-hasPermi="['device:info:edit']"
          >预览
          </el-button>
        </template>
      </el-table-column>
      <!--<el-table-column label="创建时间" align="center" prop="createTime" width="180">-->
      <!--<template slot-scope="scope">-->
      <!--<span>{{ parseTime(scope.row.createTime) }}</span>-->
      <!--</template>-->
      <!--</el-table-column>-->
      <el-table-column label="存储状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enable"
            :active-value="1"
            :inactive-value="0"
            @change="enableChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="传输状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.transferStatus"
            :active-value="1"
            :inactive-value="0"
            @change="changeTrStatus(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:info:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['device:info:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px">
      <el-steps :active="active" finish-status="success">
        <el-step title="新增设备"></el-step>
        <el-step title="传输设置"></el-step>
      </el-steps>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" v-loading="submitLoading">
        <template v-if="active === 0">
          <el-form-item label="设备名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入设备名称"/>
          </el-form-item>
          <el-form-item label="连接方式" prop="connectType">
            <el-select v-model="form.connectType" placeholder="请选择" @change="connectChange" :disabled="isUpdate">
              <el-option
                v-for="dt in connectTypeOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="绑定采集终端" prop="mac" v-show="macVisible">
            <el-select v-model="form.mac" placeholder="请选择" @change="amChange" :disabled="isUpdate">
              <el-option
                v-for="am in amOptions"
                :key="am.mac"
                :label="am.name"
                :value="am.mac"
              ></el-option>
            </el-select>

          </el-form-item>
          <el-form-item label="串口名称" prop="serialPort" v-show="seriVisible">
            <el-select v-model="form.serialPort" placeholder="请选择串口" :disabled="isUpdate" @change="serialPortChange">
              <el-option
                v-for="sp in serialPortOptions"
                :key="sp"
                :label="sp"
                :value="sp"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="IP" prop="ip" v-show="ipVisible">
            <el-input v-model="form.ip" placeholder="请输入IP" :disabled="true"/>
          </el-form-item>
          <el-form-item label="端口" prop="port" v-show="portVisible">
            <el-input v-model="form.port" placeholder="请输入端口" :disabled="true"/>
          </el-form-item>
          <el-form-item label="波特率" prop="baudRate" v-show="baudRateVisible">
            <el-select v-model="form.baudRate" placeholder="请选择波特率">
              <el-option
                v-for="dt in baudRateOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="数据位" prop="dataBits" v-show="dataBitsVisible">
            <el-select v-model="form.dataBits" placeholder="请输入数据位">
              <el-option
                v-for="dt in dataBitsOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
            <!--<el-input v-model="form.dataBits" placeholder="请输入数据位"/>-->
          </el-form-item>

          <el-form-item label="停止位" prop="stopBits" v-show="stopBitsVisible">
            <el-select v-model="form.stopBits" placeholder="请输入停止位">
              <el-option
                v-for="dt in stopBitsOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
            <!--<el-input v-model="form.stopBits" placeholder="请输入停止位"/>-->
          </el-form-item>

          <el-form-item label="校验位" prop="parity" v-show="parityVisible">
            <el-select v-model="form.parity" placeholder="请输入校验位">
              <el-option
                v-for="dt in parityOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
            <!--<el-input v-model="form.parity" placeholder="请输入校验位"/>-->
          </el-form-item>
          <el-form-item label="数据模板" prop="type">
            <el-select v-model="form.type" placeholder="请选择" @change="typeChange">
              <el-option
                v-for="dt in deviceTypeOptions"
                :key="dt.dictValue"
                :label="dt.dictLabel"
                :value="dt.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>
        <template v-if="active === 1">
          <div class="edit_dev">
            <el-transfer
              :titles="['未选属性', '已选属性']"
              v-model="form.transferAttributes"
              :props="{
              key: 'name'
            }"
              :data="allAttributes">
            </el-transfer>
          </div>
          <el-form-item label="传输间隔" prop="compartment">
            <el-input v-model="form.compartment" placeholder="请输入传输间隔" v-show="false"/>
            <el-select v-model="compartmentNum">
              <el-option
                v-for="num in numOptions"
                :key="num"
                :label="num"
                :value="num"
              ></el-option>
            </el-select>
            <el-select v-model="compartmentUnit">
              <el-option
                v-for="unit in unitOptions"
                :key="unit.val"
                :label="unit.label"
                :value="unit.val"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="优先级" prop="cost">
            <el-select v-model="form.cost" placeholder="请选择优先级">
              <el-option
                v-for="co in costOptions"
                :key="co"
                :label="co"
                :value="co"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          size="mini"
          type="text"
          @click="openRealTimeData(form)"
          v-hasPermi="['device:info:edit']"
        >预览
        </el-button>
        <el-button
          size="mini"
          type="text"
          @click="openRealAnalysisData(form)"
          v-hasPermi="['device:info:edit']"
        >解析预览
        </el-button>
        <el-button style="margin-top: 12px;" v-show="active === 1" @click="back">上一步</el-button>
        <el-button style="margin-top: 12px;" v-show="active === 0" @click="next">下一步</el-button>
        <el-button type="primary" v-show="active === 1" @click="submitForm" :disabled="submitLoading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!--实时数据弹出框-->
    <el-dialog :title="rtTitle" :visible.sync="rtOpen" width="800px" @close="rtCancel">
      <div>
        <div>
          <el-button type="primary" @click="initWebSocket" v-show="!wsConnect">连接</el-button>
          <el-button type="danger" @click="closeWebsocket" v-show="wsConnect">暂停</el-button>
          <el-button type="primary" @click="cleanShowDiv">清空</el-button>
        </div>

        <div class="show" id="showDiv"></div>
      </div>
    </el-dialog>
    <!--解析预览数据弹出框-->
    <el-dialog :title="rtsTitle" :visible.sync="rtsOpen" width="800px" @close="rsCancel">
      <el-table :data="wsServiceList" :v-loading="true">
        <!--<el-table-column  label="测试1" prop="herot" :show-overflow-tooltip="true" align="center"/>-->
        <template v-for="item in tableHeads">
          <template v-if="item.property === 'initialBjTime'">
            <el-table-column width="150px" :label="item.label" :property="item.property"
                             :show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">{{scope.row[scope.column.property]}}</template>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column :label="item.label" :property="item.property"
                             :show-overflow-tooltip="true" align="center">
              <template slot-scope="scope">{{scope.row[scope.column.property]}}</template>
            </el-table-column>
          </template>
        </template>
      </el-table>

    </el-dialog>
    <el-dialog :title="rtsBeforeTitle" :visible.sync="rtsBeforeOpen" width="160px" height="150px" >
      <div class="demo-progress" id="progress">
        <el-progress type="dashboard" :percentage="percentage2" :color="colors"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    addDevice,
    delDevice,
    disable,
    enable,
    exportDevice,
    getDevice,
    listDevice,
    unboundList,
    allAmList,
    updateDevice,
    changeTrStatus,
    getFreeSerialPort,
    attributeList,
    transferAttributeList,
    serialPortView,
    delSerialPortView,
    getTableHead,
    getTypeTableHead,
    listDefaultsByType
  } from '@/api/device/device'
  import $ from 'jquery'

  export default {
    name: 'Device',
    data() {
      return {
        attributeList,
        active: 0,
        // 遮罩层
        loading: true,
        submitLoading: false,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // 参数表格数据
        deviceList: [],
        allAttributes: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        isUpdate: false,
        isTypeShow: true,
        // 类型数据字典
        typeOptions: [],
        // 日期范围
        dateRange: [],
        // 采集终端
        amOptions: [],
        // 设备类型
        deviceTypeOptions: [],
        // 连接方式
        connectTypeOptions: [],
        //波特率
        baudRateOptions: [],
        //数据位
        dataBitsOptions: [],
        //停止位
        stopBitsOptions: [],
        //校验位
        parityOptions: [],
        // 优先级
        costOptions: [10, 20, 30, 40, 50],
        // 传输间隔
        numOptions: [1, 2, 3, 4, 5, 6, 10, 12, 15, 20, 30],
        // 传输间隔
        unitOptions: [{val: 1, label: '秒'}, {val: 2, label: '分'}],
        compartmentNum: 1,
        compartmentUnit: 1,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          name: undefined,
          ip: undefined
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          name: [
            {required: true, message: '设备名称不能为空', trigger: 'blur'}
          ],
          cost: [
            {required: true, message: '优先级不能为空', trigger: 'blur'}
          ],
          type: [
            {required: true, message: '数据模板不能为空', trigger: 'blur'}
          ],
          connectType: [
            {required: true, message: '连接方式不能为空', trigger: 'blur'}
          ]
        },
        rtTitle: '',
        rtOpen: false,
        rsCode: '',
        rtsTitle: '',
        rtsBeforeTitle: '',
        rtsBeforeOpen: false,
        rtsOpen: false,
        rtMac: '',
        wsType: 0,
        deviceType: null,
        socket: null,
        sid: null,
        wsConnect: false,
        wsMessageLength: 0,
        preVisible: 'none',
        jxVisible: 'none',
        macVisible: true,
        typeVisible: true,
        ipVisible: true,
        portVisible: true,
        seriVisible: false,
        baudRateVisible: false,
        dataBitsVisible: false,
        stopBitsVisible: false,
        parityVisible: false,
        // 串口名称
        serialPortOptions: [],
        oldType: '',
        // 解析预览表格数据
        wsServiceList: [],
        tableHeads: [],
        percentage2: 0,
        interval:null,
        colors: [
          {color: '#f56c6c', percentage: 20},
          {color: '#e6a23c', percentage: 40},
          {color: '#5cb87a', percentage: 60},
          {color: '#1989fa', percentage: 80},
          {color: '#6f7ad3', percentage: 100},
        ]
      }
    },
    created() {
      this.getDicts("device_type").then(response => {
        for (let i = 0; i < response.data.length; i++) {
          let typeMap = {dictValue: 0, dictLabel: ''};
          typeMap.dictValue = parseInt(response.data[i].dictValue);
          typeMap.dictLabel = response.data[i].dictLabel;
          this.deviceTypeOptions[i] = typeMap;
        }
      });
      this.getDicts("connect_type").then(response => {
        for (let i = 0; i < response.data.length; i++) {
          let typeMap = {dictValue: 0, dictLabel: ''};
          typeMap.dictValue = parseInt(response.data[i].dictValue);
          typeMap.dictLabel = response.data[i].dictLabel;
          this.connectTypeOptions[i] = typeMap;
        }
      });
      this.getDicts("baud_rate").then(response => {
        for (let i = 0; i < response.data.length; i++) {
          let typeMap = {dictValue: 0, dictLabel: ''};
          typeMap.dictValue = parseInt(response.data[i].dictValue);
          typeMap.dictLabel = response.data[i].dictLabel;
          this.baudRateOptions[i] = typeMap;
        }
      });
      this.getDicts("date_bits").then(response => {
        for (let i = 0; i < response.data.length; i++) {
          let typeMap = {dictValue: 0, dictLabel: ''};
          typeMap.dictValue = parseInt(response.data[i].dictValue);
          typeMap.dictLabel = response.data[i].dictLabel;
          this.dataBitsOptions[i] = typeMap;
        }
      });
      this.getDicts("stop_bits").then(response => {
        for (let i = 0; i < response.data.length; i++) {
          let typeMap = {dictValue: 0, dictLabel: ''};
          typeMap.dictValue = parseInt(response.data[i].dictValue);
          typeMap.dictLabel = response.data[i].dictLabel;
          this.stopBitsOptions[i] = typeMap;
        }
      });
      this.getDicts("parity").then(response => {
        for (let i = 0; i < response.data.length; i++) {
          let typeMap = {dictValue: 0, dictLabel: ''};
          typeMap.dictValue = parseInt(response.data[i].dictValue);
          typeMap.dictLabel = response.data[i].dictLabel;
          this.parityOptions[i] = typeMap;
        }
      }).then(this.getList());
    },
    methods: {
      back() {
        if (this.active-- < 0) this.active = 0;
        this.jxVisible = 'none';
      },
      next() {
        if(this.form.type === undefined || this.form.type === '' || this.form.type == null){
          this.msgError('数据模板不能为空!');
          return;
        }
        this.jxVisible = '';
        if (this.active++ > 1) this.active = 0;
        let _this = this;
        _this.form.transferAttributes = [];
        this.getAttributeList(this.form.type);

        if (this.form.code === undefined) {
          //使用默认得属性20211027
          // listDefaultsByType
          listDefaultsByType(this.form.type).then(response => {
            response.data.forEach(item => {
              _this.form.transferAttributes.push(item.name);
            });
            }
          );
        } else {
          transferAttributeList(this.form.code).then(response => {
              if (this.oldType === this.form.type) {
                response.data.forEach(item => {
                  _this.form.transferAttributes.push(item.name);
                });
              } else {
                //如果是修改状态下-又修改了模板则使用默认得属性
                listDefaultsByType(this.form.type).then(response => {
                  response.data.forEach(item => {
                    _this.form.transferAttributes.push(item.name);
                  });
                  }
                );
              }
              if (this.form.compartment >= 60) {
                this.compartmentNum = this.form.compartment / 60;
                this.compartmentUnit = 2;
              } else {
                this.compartmentNum = this.form.compartment;
                this.compartmentUnit = 1;
              }
            }
          );
        }


      },
      getAttributeList(type) {
        attributeList(type).then(response => {
            this.allAttributes = response.data;
          }
        )
      },
      /** 查询设备列表 */
      getList() {
        this.loading = true;
        let _this = this;
        listDevice(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
            this.deviceList = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        )
      },
      unboundList() {
        this.loading = true;
        unboundList().then(response => {
            this.amOptions = response.data;
            this.loading = false;
          }
        )
      },
      allAmList() {
        this.loading = true;
        allAmList().then(response => {
            this.amOptions = response.data;
            this.loading = false;
          }
        )
      },
      amChange() {
        for (let i = 0; i < this.amOptions.length; i++) {
          if (this.amOptions[i].mac === this.form.mac) {
            // this.form.type = this.amOptions[i].type;
            this.form.ip = this.amOptions[i].ip;
            this.form.port = this.amOptions[i].port;
          }
        }
        this.form.mac = this.form.mac.trim();
        if (this.form.mac !== null && this.form.mac !== '' && this.form.mac !== undefined) {
          this.preVisible = '';
        } else {
          this.preVisible = 'none';
        }

      },
      serialPortChange() {
        if (this.form.serialPort !== null && this.form.serialPort !== '' && this.form.serialPort !== undefined) {
          this.preVisible = '';
        } else {
          this.preVisible = 'none';
        }
      },
      typeChange() {
        if (this.form.type !== null && this.form.type !== '' && this.form.type !== undefined) {
          // this.jxVisible = '';
          this.isTypeShow = false;
        } else {
          // this.jxVisible = 'none';
          this.isTypeShow = true;
        }
        //调用设备对应类型的参数返回
        // if (this.form.id === undefined) {
        getTypeTableHead(this.form.type).then(response => {
          this.tableHeads = response.data;
        });
        // }
      },
      connectChange() {
        let _this = this;
        for (let i = 0; i < this.connectTypeOptions.length; i++) {
          if (this.connectTypeOptions[i].dictValue === this.form.connectType && this.form.connectType == 0) {
            //采集终端-禁用显示串口连接的属性
            _this.macVisible = true;
            _this.typeVisible = true;
            _this.ipVisible = true;
            _this.portVisible = true;
            _this.seriVisible = false;
            _this.baudRateVisible = false;
            _this.dataBitsVisible = false;
            _this.stopBitsVisible = false;
            _this.parityVisible = false;
          } else if (this.connectTypeOptions[i].dictValue === this.form.connectType && this.form.connectType == 1) {
            //串口连接-禁用显示采集终端相关属性
            _this.macVisible = false;
            _this.typeVisible = false;
            _this.ipVisible = false;
            _this.portVisible = false;
            _this.seriVisible = true;
            _this.baudRateVisible = true;
            _this.dataBitsVisible = true;
            _this.stopBitsVisible = true;
            _this.parityVisible = true;
          }
        }
      },
      enableChange(row) {
        let that = this;
        let text = row.enable === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.name + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return row.enable === 0 ? that.disable(row) : that.enable(row);
        }).then(() => {
          this.msgSuccess(text + '成功');
        }).catch(function () {
          row.enable = row.enable === 0 ? 1 : 0;
        })
      },
      deviceTypeFormat(row, column) {
        if (row.type === undefined || row.type === '' || row.type === null) {
          return '';
        }
        return this.selectDictLabel(this.deviceTypeOptions, row.type.toString());
      },
      connectTypeFormat(row, column) {
        if (row.connectType === undefined || row.connectType === '' || row.connectType === null) {
          return '';
        }
        return this.selectDictLabel(this.connectTypeOptions, row.connectType.toString());
      },
      connectStatusFormat(row, column) {
        if (row.connectStatus === 1) {
          return "连接";
        }
        return "中断";
      },
      interfaceFormat(row, column) {
        if (row.connectType === 0) {
          //采集终端
          return row.ip + ":" + row.port;
        } else if (row.connectType === 1) {
          //串口连接
          return row.serialPort + "," + row.baudRate + "," + row.dataBits + "," + row.stopBits + "," + row.parity;
        }
        //其他
        return "";
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: undefined,
          name: undefined,
          type: undefined,
          ip: undefined,
          port: undefined,
          code: undefined,
          connectType: 0,
          transferAttributes: [],
          cost: 10
        };
        this.resetForm('form');
        this.submitLoading = false;
        this.macVisible = true;
        this.typeVisible = true;
        this.ipVisible = true;
        this.portVisible = true;
        this.seriVisible = false;
        this.baudRateVisible = false;
        this.dataBitsVisible = false;
        this.stopBitsVisible = false;
        this.parityVisible = false;
        this.active = 0;
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.dateRange = [];
        this.resetForm('queryForm');
        this.handleQuery();
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = '添加设备';
        this.isUpdate = false;
        this.unboundList();
        this.getFreeSerialPort();
        this.form.connectType = 0;//默认采集终端
        // this.getAttributeList(this.form.type);
        if (this.form.mac !== null && this.form.mac !== '' && this.form.mac !== undefined) {
          this.preVisible = '';
        } else {
          this.preVisible = 'none';
        }
        if (this.form.type !== null && this.form.type !== '' && this.form.type !== undefined) {
          this.jxVisible = '';
          this.isTypeShow = false;
        } else {
          this.jxVisible = 'none';
          this.isTypeShow = true;
        }

      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },
      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset();
        this.allAmList();
        this.getFreeSerialPort();
        this.isUpdate = true;
        const id = row.id || this.ids;
        this.oldType = row.type;
        this.rsCode = row.code;
        getDevice(id).then(response => {
          this.form = response.data;
          this.open = true;
          this.title = '修改参数';
          if (response.data.mac !== null && response.data.mac !== '' && response.data.mac !== undefined) {
            this.preVisible = '';
          } else {
            this.preVisible = 'none';
          }
          if (response.data.type !== null && response.data.type !== '' && response.data.type !== undefined) {
            this.jxVisible = '';
            this.isTypeShow = false;
          } else {
            this.jxVisible = 'none';
            this.isTypeShow = true;
          }
        });
        getTableHead(id).then(response => {
          this.tableHeads = response.data;
        });

        if (row.connectType === 0) {
          //采集终端-禁用显示串口连接的属性
          this.macVisible = true;
          this.typeVisible = true;
          this.ipVisible = true;
          this.portVisible = true;
          this.seriVisible = false;
          this.baudRateVisible = false;
          this.dataBitsVisible = false;
          this.stopBitsVisible = false;
          this.parityVisible = false;
        } else if (row.connectType === 1) {
          //串口连接-禁用显示采集终端相关属性
          this.macVisible = false;
          this.typeVisible = false;
          this.ipVisible = false;
          this.portVisible = false;
          this.seriVisible = true;
          this.baudRateVisible = true;
          this.dataBitsVisible = true;
          this.stopBitsVisible = true;
          this.parityVisible = true;
        }
      },
      /** 提交按钮 */
      submitForm: function () {
        this.compartmentChange();
        this.submitLoading = true;
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.id !== undefined) {
              updateDevice(this.form).then(response => {
                if (response.code === 200) {
                  this.msgSuccess('修改成功');
                  this.open = false;
                  this.getList();
                } else {
                  this.submitLoading = false;
                  this.msgError(response.msg);
                }
              })
            } else {
              addDevice(this.form).then(response => {
                if (response.code === 200) {
                  this.msgSuccess('新增成功');
                  this.open = false;
                  this.getList();
                } else {
                  this.submitLoading = false;
                  this.msgError(response.msg);
                }
              })
            }
          }
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ids = row.id || this.ids;
        let that = this;
        this.$confirm('是否确认删除选择的数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          that.loading = true;
          return delDevice(ids);
        }).then(() => {
          that.getList();
          that.msgSuccess('删除成功');
          that.loading = false;
        }).catch(function () {
          that.loading = false;
        })
      },
      /** 导出按钮操作 */
      handleExport() {
        const queryParams = this.queryParams;
        this.$confirm('是否确认导出所有数据项?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return exportDevice(queryParams)
        }).then(response => {
          this.download(response.msg)
        }).catch(function () {
        })
      },
      /** 启用 */
      enable(row) {
        return enable(row.id);
      },
      /** 禁用 */
      disable(row) {
        return disable(row.id);
      },
      /** 数据窗口弹出 */
      openRealTimeData(row) {
        if(row.type === undefined ||row.type === '' || row.type == null){
          this.msgError('数据模板不能为空!');
          return;
        }
        this.rtOpen = true;
        this.rtTitle = '实时数据';
        this.wsType = 0;
        this.deviceType = row.type;
        this.rsCode = row.code;
        if (row.connectType === 0) {
          this.rtMac = row.mac;//采集终端预览
          this.initWebSocket();
        } else {

          //当为修改时直接开启websocket监听
          if (row.code !== null && row.code !== '' && row.code !== undefined) {
            this.rtMac = row.serialPort;//串口预览
            this.initWebSocket();
          } else {
            //请求后端建立串口连接，开启监听，然后返回再开启websocket监听
            serialPortView(row).then(response => {
              this.rtMac = row.serialPort;//串口预览
              this.initWebSocket();
            });

          }

        }

      },
      /** 解析数据窗口弹出 */
      openRealAnalysisData(row) {
        if(row.type === undefined ||row.type === '' || row.type == null){
          this.msgError('数据模板不能为空!');
          return;
        }
        this.deviceType = this.form.type;
        this.percentage2 = 0;
        this.rtsBeforeOpen = true;
        this.rtsBeforeTitle = '';
        clearInterval(this.interval);
        this.interval = null;
        this.wsType = 1;
        if (row.connectType === 0) {
          this.rtMac = row.mac;//采集终端解析预览
          this.initWebSocket();
        } else {
          //当为修改时直接开启websocket监听
          if (row.code !== null && row.code !== '' && row.code !== undefined) {
            this.rtMac = row.serialPort;//串口预览
            this.initWebSocket();
          } else {
            //请求后端建立串口连接，开启监听，然后返回再开启websocket监听
            serialPortView(row).then(response => {
              this.rtMac = row.serialPort;//串口解析预览
              this.initWebSocket();
            });

          }
        }

        this.interval = setInterval(() => {
          this.percentage2 = (this.percentage2 % 100) + 1;
          if (this.percentage2 === 100) {
            clearInterval(this.interval);
            this.rtsBeforeOpen = false;

          }
        }, 1000);


      },
      /** 数据窗口关闭 */
      rtCancel() {
        this.closeWebsocket();
        this.cleanShowDiv();
        this.rtOpen = false;
        if (this.form.connectType === 1) {
          if (this.rsCode === undefined || this.rsCode === '') {
            //串口类型-新增的时候触发-关闭监听
            delSerialPortView(this.form).then(response => {
              this.rtMac = this.form.serialPort;//串口预览
            });
          }
        }

      },
      /** 解析预览数据窗口关闭 */
      rsCancel() {
        this.closeWebsocket();
        this.cleanShowDiv();
        this.rtsOpen = false;
        if (this.form.connectType === 1) {
          if (this.rsCode === undefined || this.rsCode === '') {
            //串口类型-新增的时候触发-关闭监听
            delSerialPortView(this.form).then(response => {
              this.rtMac = this.form.serialPort;//串口解析预览
            });
          }
        }

      },
      //初始化websocket
      initWebSocket() {
        this.sid = 'sid-' + (new Date()).getTime();
        const wsUri = 'ws://' + window.location.host + ':9991/wsServer/' + this.sid + '/' + this.rtMac + '/' + this.wsType + '/' + this.deviceType;
        this.socket = new WebSocket(wsUri);
        this.socket.onmessage = this.websocketOnmessage;
        this.socket.onopen = this.websocketOnopen;
        this.socket.onerror = this.websocketOnerror;
        this.socket.onclose = this.websocketClose;

        this.monitorFun(1);
      },
      websocketOnopen() { //连接建立之后执行send方法发送数据
        console.log('websocket连接成功');
        this.wsConnect = true;
      },
      websocketOnerror() {//连接建立失败重连
        console.log('websocket连接失败');
        this.wsConnect = false;
      },
      websocketOnmessage(e) { //数据接收
        // this.wsServiceList = [];
        this.wsMessageLength++;
        if (this.wsType == 1) {

          this.rtsBeforeOpen = false;
          //解析预览显示处理
          if (e.data === 'null') {
            this.msgError('请选择正确模板.');
            this.percentage2 = 100;
            this.rtsOpen = false;
            this.closeWebsocket();
          } else {
            this.percentage2 = 100;
            if (this.rtsBeforeOpen === false) {
              this.rtsOpen = true;
              this.rtsTitle = '解析预览数据';
            }
            this.wsServiceList.push(JSON.parse(e.data));
            if (this.wsMessageLength > 10) {
              this.wsServiceList.splice(0, 1);
            }
          }

        } else {
          if (this.wsMessageLength === 1) {
            $('.show').append('<p id="sm_' + this.wsMessageLength + '">' + e.data + '</p>');
          } else {
            $('#sm_' + (this.wsMessageLength - 1)).after('<p id="sm_' + this.wsMessageLength + '">' + e.data + '</p>');
          }

          if (this.wsMessageLength > 1000) {
            $('#sm_' + (this.wsMessageLength - 1000)).remove();
          }

          let showDiv = document.getElementById('showDiv');
          showDiv.scrollTop = showDiv.scrollHeight;
        }

      },
      websocketClose() {
        console.log('websocket断开连接');
        this.wsConnect = false;
      },
      closeWebsocket() {
        this.socket.close();
        this.wsServiceList = [];
      },
      cleanShowDiv() {
        $('.show').empty();
        this.wsMessageLength = 0;
      },
      monitorFun(t) {
        let time = t || 2;
        let userTime = time * 60;
        let that = this;
        let objTime = {
          init: 0,
          time: function () {
            objTime.init += 1;
            if (!that.wsConnect) {
              objTime.destroyFun();
            }

            if (objTime.init === userTime) {
              that.closeWebsocket();
              objTime.destroyFun();
            }
          },
          eventFun: function () {
            objTime.clearFun();
            monitorTest = setInterval(objTime.time, 1000);
          },
          clearFun: function () {
            clearInterval(monitorTest);
            objTime.init = 0;
          },
          destroyFun: function () {
            objTime.clearFun();
            $('#deviceDiv').unbind('click').unbind('keydown').unbind('mousemove').unbind('mousewheel');
          }
        };
        let monitorTest = setInterval(objTime.time, 1000);

        $('#deviceDiv').bind('click', objTime.eventFun).bind('keydown', objTime.eventFun)
          .bind('mousemove', objTime.eventFun).bind('mousewheel', objTime.eventFun);
      },
      /** 传输状态修改*/
      changeTrStatus(row) {
        let that = this;
        let text = row.transferStatus === 0 ? '停用' : '启用';
        this.$confirm('确认要' + text + row.name + '吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function () {
          return changeTrStatus(row.id, row.transferStatus);
        }).then(() => {
          this.msgSuccess(text + '成功');
        }).catch(function () {
          row.transferStatus = row.transferStatus === 0 ? 1 : 0;
        });
      },
      getFreeSerialPort() {
        this.loading = true;
        getFreeSerialPort().then(response => {
            this.serialPortOptions = response.data;
            this.loading = false;
          }
        )
      },
      compartmentChange() {
        this.form.compartment = this.compartmentNum;
        if (this.compartmentUnit === 2) {
          this.form.compartment = this.compartmentNum * 60;
        }
      }
    }
  }
</script>


<style>
  .show {
    width: 100%;
    height: 370px;
    overflow-y: scroll;
    padding: 10px;
    background-color: black;
    color: white;
    margin: 3px 0;
  }

  .edit_dev {
    padding-bottom: 20px;
    padding-top: 20px;
  }

  .edit_dev >>> .el-transfer-panel {
    width: 320px;
  }

</style>
